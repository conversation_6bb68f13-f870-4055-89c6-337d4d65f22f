--cpu=Cortex-M4.fp.sp
"bsp_ileak_heu\startup_stm32f407xx.o"
"bsp_ileak_heu\main.o"
"bsp_ileak_heu\gpio.o"
"bsp_ileak_heu\can.o"
"bsp_ileak_heu\stm32f4xx_it.o"
"bsp_ileak_heu\stm32f4xx_hal_msp.o"
"bsp_ileak_heu\stm32f4xx_hal_can.o"
"bsp_ileak_heu\stm32f4xx_hal_rcc.o"
"bsp_ileak_heu\stm32f4xx_hal_rcc_ex.o"
"bsp_ileak_heu\stm32f4xx_hal_flash.o"
"bsp_ileak_heu\stm32f4xx_hal_flash_ex.o"
"bsp_ileak_heu\stm32f4xx_hal_flash_ramfunc.o"
"bsp_ileak_heu\stm32f4xx_hal_gpio.o"
"bsp_ileak_heu\stm32f4xx_hal_dma_ex.o"
"bsp_ileak_heu\stm32f4xx_hal_dma.o"
"bsp_ileak_heu\stm32f4xx_hal_pwr.o"
"bsp_ileak_heu\stm32f4xx_hal_pwr_ex.o"
"bsp_ileak_heu\stm32f4xx_hal_cortex.o"
"bsp_ileak_heu\stm32f4xx_hal.o"
"bsp_ileak_heu\stm32f4xx_hal_exti.o"
"bsp_ileak_heu\system_stm32f4xx.o"
--strict --scatter "BSP_ILEAK_HEU\BSP_ILEAK_HEU.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "BSP_ILEAK_HEU.map" -o BSP_ILEAK_HEU\BSP_ILEAK_HEU.axf