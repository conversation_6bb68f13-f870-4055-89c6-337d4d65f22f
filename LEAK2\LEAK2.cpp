/************************************************************/
/*    NAME: zhaoqinchao                                    */
/*    ORGN: HEU                                             */
/*    FILE: LEAK2.cpp                                      */
/*    DATE: 2025/07/18                                      */
/************************************************************/

#include <iterator>
#include <pthread.h>
#include <cstdlib>
#include <cerrno>
#include <cstring>
#include <algorithm>
#include <vector>
#include <string>
#include <cctype>
#include "MBUtils.h"
#include "LEAK2.h"

using namespace std;
bool LEAK2::OnStartUp()
{
    string sRecvIP; // 从配置文件中读取 接收IP    本机监听地址     3.5版本 ************   现在************
    if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
    {
        MOOSTrace("cannot get RecvIP \n");
        sRecvIP = "0.0.0.0";
    }

    int iRecvPort; // 从配置文件中读取 接收端口
    if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
    {
        MOOSTrace("cannot get RecvPort \n");
        return false;
    }

    string sDestIP;                                                // 从配置文件中读取 目标IP 0.96     本机监听地址can网卡，can网卡接收到数据后，can网卡将数据发送给can网络上的其他设备
    if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP)) // 从配置文件中读取参数，如果读取失败则存储到变量中，如果读取失败则返回
    {
        MOOSTrace("cannot get DestIP \n");
        return false;
    }

    int iDestPort; // 从配置文件中读取 目标端口
    if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
    {
        MOOSTrace("cannot get DestPort \n");
        return false;
    }

    // 读取容错机制配置参数
    if (!m_MissionReader.GetConfigurationParam("max_retries", m_max_retries))
    {
        MOOSTrace("cannot get max_retries, using default: %d \n", m_max_retries);
    }

    if (!m_MissionReader.GetConfigurationParam("retry_delay", m_retry_delay))
    {
        MOOSTrace("cannot get retry_delay, using default: %d \n", m_retry_delay);
    }

    // 使用容错机制打开接收套接字
    if (!RecvSock.OpenSocketWithRetry(sRecvIP, iRecvPort, m_max_retries, m_retry_delay))
    {
        MOOSTrace("Failed to open RecvSocket after %d retries \n", m_max_retries);
        return false;
    }

    // 绑定接收套接字，失败时重试
    if (!RecvSock.BindSocket())
    {
        MOOSTrace("Failed to bind RecvSocket, attempting rebind with retry \n");
        if (!RecvSock.RebindSocket(m_max_retries, m_retry_delay))
        {
            MOOSTrace("Failed to rebind RecvSocket after %d retries \n", m_max_retries);
            return false;
        }
    }

    // 使用容错机制打开发送套接字
    if (!SendSock.OpenSocketWithRetry(sDestIP, iDestPort, m_max_retries, m_retry_delay))
    {
        MOOSTrace("Failed to open SendSocket after %d retries \n", m_max_retries);
        return false;
    }

    // 启动接收线程
    m_thread_running = true;
    if (pthread_create(&m_recv_thread, NULL, LEAK2::RecvFrameWrapper, this) != 0) // 如果 pthread_create 函数返回0，则表示创建成功
    {
        MOOSTrace("[LEAK] Failed to create recv thread\n");
        m_thread_running = false;
        return false;
    }

    MOOSTrace("[LEAK] Recv thread created successfully\n");

    // 添加漏水检测MOOS变量 - 发布LEAK2变量
    AddMOOSVariable("LEAK2", "LEAK2", "", 0.1);

    m_timewarp = GetMOOSTimeWarp();

    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// Procedure: RegisterVariables

void LEAK2::RegisterVariables()
{
    // 注册监听LEAK1变量，用于接收iThruster_HEU模块的尾端漏水检测数据
    Register("LEAK1", 0);
    MOOSTrace("[LEAK] 已注册监听LEAK1变量\n");

    // 注册监听设备控制变量 - 已删除，只保留漏水检测功能
    // Register("LOAD_ACTION", 0);
    // Register("CONTROL_MSG", 0);
    // MOOSTrace("[LEAK] 已注册监听LOAD_ACTION和CONTROL_MSG变量\n");
}
//---------------------------------------------------------
// Procedure: OnNewMail
// 处理新邮件的回调函数，继承自MOOSApp基类的虚函数
bool LEAK2::OnNewMail(MOOSMSG_LIST &NewMail)
{
    // MOOSTrace("This is OnNewMail\n");
    UpdateMOOSVariables(NewMail);
    // MOOSTrace("NewMail Size = %d\n", NewMail.size());

    // 处理接收到的MOOS变量
    MOOSMSG_LIST::iterator p;
    for (p = NewMail.begin(); p != NewMail.end(); p++)
    {
        CMOOSMsg &rMsg = *p;

        // 处理LEAK1变量（来自iThruster_HEU模块的尾端漏水检测）
        if (rMsg.GetKey() == "LEAK1")
        {
            m_leak1_status = rMsg.GetDouble();
            MOOSTrace("[LEAK] 接收到LEAK1变量: %.0f (0=正常, 64=尾端漏水故障)\n", m_leak1_status);
            // 接收到LEAK1变量后，立即发布综合漏水状态
            PublishCombinedLeakStatus();
        }

        // 处理LOAD_ACTION变量（抛载控制） - 已删除，只保留漏水检测功能
        /*
        else if (rMsg.GetKey() == "LOAD_ACTION")
        {
            // 处理字符串类型的"true"和"false"
            string load_str = rMsg.GetString();
            MOOSTrimWhiteSpace(load_str);
            MOOSToLower(load_str);
            m_load_action = (load_str == "true");
            MOOSTrace("[LEAK] 接收到LOAD_ACTION变量: %s\n", m_load_action ? "true" : "false");
            HandleLoadAction(m_load_action);
        }

        // 处理CONTROL_MSG变量（设备控制消息）
        else if (rMsg.GetKey() == "CONTROL_MSG")
        {
            m_control_msg = rMsg.GetString();
            MOOSTrace("[LEAK] 接收到CONTROL_MSG变量: %s\n", m_control_msg.c_str());
            HandleControlMessage(m_control_msg);
        }
        */
    }

    // 返回 true 表示处理成功
    return (true);
}

//---------------------------------------------------------
// 静态线程包装函数 - 类的静态成员函数
void *LEAK2::RecvFrameWrapper(void *arg)
{
    LEAK2 *instance = static_cast<LEAK2 *>(arg);
    instance->RecvFrame();
    return nullptr;
}

//---------------------------------------------------------
// Constructor

LEAK2::LEAK2()
{
    m_iterations = 0;
    m_timewarp = 1;
    SetAppFreq(10); // 设置应用程序和通信频率
    SetCommsFreq(10);

    // 容错机制参数初始化
    m_max_retries = 5; // 默认最大重试次数
    m_retry_delay = 2; // 默认重试延迟2秒

    // 初始化线程控制变量
    m_thread_running = false;
    m_recv_thread = 0;

    // 初始化漏水检测变量
    m_leak_detected = false;
    m_leak_checksum = 0;
    m_calculated_checksum = 0;
    m_leak1_status = 0.0; // 初始化LEAK1变量
    for (int i = 0; i < 4; i++)
    {
        m_sensor_status[i] = false;
    }

    // 初始化设备控制变量 - 已删除，只保留漏水检测功能
    /*
    m_load_action = false;
    m_control_msg = "";
    m_control_frame_ready = false;
    for (int i = 0; i < 10; i++)
    {
        m_device_control[i] = 0x00; // 所有设备默认关闭
    }
    */
}

//---------------------------------------------------------
LEAK2::~LEAK2()
{
    // 停止接收线程
    m_thread_running = false;
    if (m_recv_thread != 0)
    {
        pthread_join(m_recv_thread, NULL);
    }
}

void LEAK2::RecvFrame() // 接收网络数据帧并转换为 CAN 帧进行处理
{
    MOOSTrace("[LEAK] RecvFrame thread started\n");

    // 循环接收网络数据帧，直到线程停止标志被设置
    while (m_thread_running)
    {
        // 定义数据帧
        vector<uint8_t> Frame;
        // 从网络套接字接收二进制数据到Frame中
        int n = RecvSock.RecvBinary(Frame, FRAME_LEN);
        // 打印接收状态：帧大小和实际接收字节数（调试用）
        // 不直接退出，而是重试
        if (n <= 0)
        {
            MOOSTrace("[LEAK] RecvFrame warning: n=%d, errno=%d (%s)\n",
                      n, errno, strerror(errno));
            MOOSPause(100); // 短暂延迟后继续
            continue;
        }

        // 成功接收到数据，解析帧
        MOOSTrace("[LEAK] ========== New Data Received ==========\n");
        MOOSTrace("[LEAK] Received %d bytes from network\n", n);
        ParseFrame(Frame);

        // MOOSTrace("FRAME %d  %d %d %d  %d %d %d  %d %d %d  %d %d %d  \n",Frame[0],Frame[1],Frame[2],Frame[3],Frame[4],Frame[5],Frame[6],Frame[7],Frame[8],Frame[9],Frame[10],Frame[11],Frame[12]);
    }

    MOOSTrace("接收的线程停止了\n");
}
// 解析接收到的网络数据帧，根据帧头类型分发到相应的处理函数，并发布到 MOOS 数据库
void LEAK2::ParseFrame(vector<uint8_t> Frame)
{
    // 打印接收到的完整数据帧（调试用）
    MOOSTrace("[LEAK] ========== Received Frame ==========\n");
    MOOSTrace("[LEAK] Frame length: %d bytes\n", Frame.size());
    MOOSTrace("[LEAK] Raw data: ");
    for (size_t i = 0; i < Frame.size(); i++)
    {
        printf("%02X ", Frame[i]);
    }
    printf("\n");

    // 验证数据帧长度是否为标准的13字节
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("帧长度无效: %d\n", Frame.size());
        return;
    }

    vector<uint8_t> FrameHeader(Frame.begin(), Frame.begin() + 5); // 提取数据帧的前 5 个字节(索引0到4)作为帧头存储到FrameHeader中

    // 打印帧头信息（调试用）
    MOOSTrace("[LEAK] Frame header: %02X %02X %02X %02X %02X\n",
              FrameHeader[0], FrameHeader[1], FrameHeader[2], FrameHeader[3], FrameHeader[4]);

    // 解析CAN ID
    uint32_t received_can_id = (FrameHeader[1] << 24) | (FrameHeader[2] << 16) | (FrameHeader[3] << 8) | FrameHeader[4];
    MOOSTrace("[LEAK] Extracted CAN ID: 0x%08X (decimal: %d)\n", received_can_id, received_can_id);

    // 定义CAN ID 0x321的帧头
    vector<uint8_t> CAN321Header = {0x08, 0x00, 0x00, 0x03, 0x21}; // CAN ID: 0x321

    // 检查是否为CAN ID 0x321的漏水检测数据帧
    if (FrameHeader == CAN321Header)
    {
        MOOSTrace("帧头匹配CAN ID 0x321\n");

        // 验证：检查数据帧的第6字节是否为0xFB（漏水检测标识符）
        if (Frame.size() >= 6)
        {
            uint8_t data_identifier = Frame[5];
            MOOSTrace("数据标识符: 0x%02X\n", data_identifier);

            if (data_identifier == 0xFB)
            {
                MOOSTrace("有效漏水检测帧\n");
                ParseLeakageFrame(Frame);
            }
            else
            {
                MOOSTrace("数据标识符无效: 0x%02X\n", data_identifier);
            }
        }
        else
        {
            MOOSTrace("帧长度不足\n");
        }
        return;
    }
    else
    {
        MOOSTrace("帧头不匹配CAN ID 0x321\n");
    }

    MOOSTrace("[LEAK] ========== Frame Processing End ==========\n");
}

// 解析漏水检测数据帧
void LEAK2::ParseLeakageFrame(vector<uint8_t> Frame)
{
    // 验证数据帧长度 (13字节网络帧)
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("[LEAK] Invalid frame length: %d, expected: %d\n", Frame.size(), FRAME_LEN);
        return;
    }
    // 完整CAN ID = (Frame[1]<<24) | (Frame[2]<<16) | (Frame[3]<<8) | Frame[4]
    uint32_t received_can_id = (Frame[1] << 24) | (Frame[2] << 16) | (Frame[3] << 8) | Frame[4];
    if (received_can_id != 0x321)
    {
        MOOSTrace("[LEAK] Invalid CAN ID: 0x%08X, expected: 0x321\n", received_can_id);
        return;
    }

    // 检查漏水数据标识符 (第6字节应为0xFB) - 对应漏水32代码中的Leakbuf[0]=0xFB
    if (Frame[5] != 0xFB)
    {
        MOOSTrace("[LEAK] Invalid leakage frame identifier: 0x%02X, expected: 0xFB\n", Frame[5]);
        return;
    }

    MOOSTrace("[LEAK] ========== Parsing Leakage Frame ==========\n");

    // 提取传感器数据 (字节6-9) - 对应漏水32代码中的4个传感器数据
    uint8_t sensor_data[4];
    for (int i = 0; i < 4; i++)
    {
        sensor_data[i] = Frame[6 + i];
    }

    MOOSTrace("[LEAK] Sensor data extracted:\n");
    MOOSTrace("[LEAK]   Sensor 1 (byte 7): 0x%02X\n", sensor_data[0]);
    MOOSTrace("[LEAK]   Sensor 2 (byte 8): 0x%02X\n", sensor_data[1]);
    MOOSTrace("[LEAK]   Sensor 3 (byte 9): 0x%02X\n", sensor_data[2]);
    MOOSTrace("[LEAK]   Sensor 4 (byte 10): 0x%02X\n", sensor_data[3]);

    // 提取接收到的校验和 (第13字节) - 对应漏水32代码中的checksum
    m_leak_checksum = Frame[12];
    MOOSTrace("[LEAK] Received checksum (byte 13): 0x%02X\n", m_leak_checksum);

    // 计算校验和 (累加和) - 只累加4个传感器数据
    m_calculated_checksum = 0;
    for (int i = 0; i < 4; i++)
    {
        m_calculated_checksum += sensor_data[i];
    }
    MOOSTrace("计算校验和: %d\n", m_calculated_checksum);

    // 验证校验和
    if (m_leak_checksum != m_calculated_checksum)
    {
        MOOSTrace("校验和错误: 接收=%d, 计算=%d\n", m_leak_checksum, m_calculated_checksum);
        // 不return，继续处理漏水检测
    }
    else
    {
        MOOSTrace("校验和验证通过\n");
    }

    // 校验通过，构造8字节CAN数据帧并发送
    // 构造13字节网络帧：[08 00 00 03 21] + [FB + 4字节传感器数据 + 2字节填充 + 校验和]
    vector<uint8_t> network_frame = {0x08, 0x00, 0x00, 0x03, 0x21, // 网络帧头 + CAN ID 0x321
                                     0xFB, sensor_data[0], sensor_data[1],
                                     sensor_data[2], sensor_data[3], 0x00,
                                     0x00, m_leak_checksum};

    // 通过BlueSocket发送13字节网络帧到CAN网络
    SendFrame(network_frame);
    MOOSTrace("网络帧已发送\n");

    // 解析传感器状态 - 根据32代码中的4个传感器
    // 传感器1: 0x01, 传感器2: 0x02, 传感器3: 0x04, 传感器4: 0x08
    uint8_t expected_values[4] = {0x01, 0x02, 0x04, 0x08};
    m_leak_detected = false;

    MOOSTrace("传感器状态分析:\n");
    for (int i = 0; i < 4; i++)
    {
        // 传感器漏水时值为expected_values[i]，正常时为0x00
        m_sensor_status[i] = (sensor_data[i] == expected_values[i]);

        MOOSTrace("传感器%d: 数据=0x%02X, 状态=%s\n",
                  i+1, sensor_data[i], m_sensor_status[i] ? "漏水" : "正常");

        if (m_sensor_status[i])
        {
            m_leak_detected = true;
        }
    }

    MOOSTrace("总体漏水状态: %s\n", m_leak_detected ? "检测到漏水" : "正常");

    // 发布综合漏水状态（本地传感器 OR 尾端漏水）
    PublishCombinedLeakStatus();
}

// 解析设备控制数据帧 - 已删除，只保留漏水检测功能
/*
void LEAK2::ParseControlFrame(vector<uint8_t> Frame)
{
    // 验证数据帧长度 (13字节网络帧)
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("[LEAK] Invalid control frame length: %d, expected: %d\n", Frame.size(), FRAME_LEN);
        return;
    }

    // 验证CAN ID (0x321)
    uint32_t received_can_id = (Frame[1] << 24) | (Frame[2] << 16) | (Frame[3] << 8) | Frame[4];
    if (received_can_id != 0x321)
    {
        MOOSTrace("[LEAK] Invalid control CAN ID: 0x%08X, expected: 0x321\n", received_can_id);
        return;
    }

    // 检查控制数据标识符 (第6字节应为0xFA)
    if (Frame[5] != 0xFA)
    {
        MOOSTrace("[LEAK] Invalid control frame identifier: 0x%02X, expected: 0xFA\n", Frame[5]);
        return;
    }

    // 提取控制数据 (字节6-11)
    for (int i = 0; i < 6; i++)
    {
        m_device_control[i] = Frame[6 + i];
    }

    // 转发控制帧到CAN网络
    SendFrame(Frame);
    MOOSTrace("[LEAK] Control frame forwarded: [0x08,0x00,0x00,0x03,0x21,0xFA,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X]\n",
              m_device_control[0], m_device_control[1], m_device_control[2],
              m_device_control[3], m_device_control[4], m_device_control[5], Frame[12]);
}
*/

//---------------------------------------------------------
// Procedure: OnConnectToServer
// 连接到MOOS服务器时的回调函数
bool LEAK2::OnConnectToServer()
{
    // register for variables here
    // possibly look at the mission file?
    // m_MissionReader.GetConfigurationParam("Name", <string>);
    // m_Comms.Register("VARNAME", 0);

    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// Procedure: Iterate()
//            happens AppTick times per second

bool LEAK2::Iterate()
{
    // 漏水检测模块主要通过接收线程处理数据
    // 此函数保持简单的心跳功能
    m_iterations++;

    // 检查是否有待发送的控制帧 - 已删除，只保留漏水检测功能
    /*
    if (m_control_frame_ready)
    {
        SendControlFrame();
    }
    */

    return (true);
}

// 发送控制帧到CAN网络 - 已删除，只保留漏水检测功能
/*
void LEAK2::SendControlFrame()
{
    // 构造13字节控制网络帧：[08 00 00 03 21] + [FA + 6字节控制数据 + 校验和]
    uint8_t checksum = 0xFA;
    for (int i = 0; i < 6; i++)
    {
        checksum += m_device_control[i];
    }

    vector<uint8_t> control_frame = {0x08, 0x00, 0x00, 0x03, 0x21, // 网络帧头 + CAN ID 0x321
                                     0xFA, m_device_control[0], m_device_control[1],
                                     m_device_control[2], m_device_control[3], m_device_control[4],
                                     m_device_control[5], checksum};

    // 发送控制帧
    SendFrame(control_frame);
    MOOSTrace("[LEAK] Control frame sent: [0x08,0x00,0x00,0x03,0x21,0xFA,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X]\n",
              m_device_control[0], m_device_control[1], m_device_control[2],
              m_device_control[3], m_device_control[4], m_device_control[5], checksum);

    // 重置控制帧准备标志
    m_control_frame_ready = false;
}
*/

void LEAK2::SendFrame(vector<uint8_t> Frame)
{
    SendSock.SendBinary(Frame);
    // MOOSTrace ("a = %d \n",a);
    m_iterations++;
    // MOOSTrace ("iterate = %d \n",iterate);
}

// 发布综合漏水状态 - 本地传感器状态与LEAK1状态"或"运算后发布
void LEAK2::PublishCombinedLeakStatus()
{
    MOOSTrace("[LEAK] ========== Publishing Combined Leak Status ==========\n");

    // 综合漏水状态：本地传感器检测 OR LEAK1（尾端漏水）
    bool overall_leak_detected = m_leak_detected || (m_leak1_status > 0);

    MOOSTrace("[LEAK] Status calculation:\n");
    MOOSTrace("[LEAK]   Local sensors leak: %s\n", m_leak_detected ? "YES" : "NO");
    MOOSTrace("[LEAK]   LEAK1 (tail) status: %.0f\n", m_leak1_status);
    MOOSTrace("[LEAK]   Overall leak detected: %s\n", overall_leak_detected ? "YES" : "NO");

    // 计算综合漏水故障码 - 传感器1-4 + 传感器7(尾端)=0x64
    double leak_code = 0;
    MOOSTrace("[LEAK] Leak code calculation:\n");
    for (int i = 0; i < 4; i++)
    {
        if (m_sensor_status[i])
        {
            leak_code += (i + 1); // 传感器1=1, 传感器2=2, 传感器3=3, 传感器4=4
            MOOSTrace("[LEAK]   Sensor %d leak: +%d\n", i+1, i+1);
        }
    }
    if (m_leak1_status > 0)
    {
        leak_code += 64.0; // 传感器7(尾端)=64
        MOOSTrace("[LEAK]   Tail leak: +64\n");
    }
    MOOSTrace("[LEAK]   Total leak code: %.0f\n", leak_code);

    // 发布综合漏水状态到MOOS数据库 - 发布LEAK2变量
    Notify("LEAK2", overall_leak_detected);
    Notify("LEAK2_CODE", leak_code);

    // 详细状态发布
    Notify("LEAK2_SENSORS", m_leak_detected); // 本地传感器状态
    Notify("LEAK2_TAIL", m_leak1_status);     // 尾端漏水状态

    // 发布告警信息 - 当检测到漏水时发布AUV_WARN_MSG
    if (overall_leak_detected)
    {
        string sWarnMsg = "ID=LEAK2;CODE=18;NOTE=Leak Detection";
        Notify("AUV_WARN_MSG", sWarnMsg);
        MOOSTrace("漏水告警已发布\n");
    }

    MOOSTrace("[LEAK] MOOS variables published:\n");
    MOOSTrace("[LEAK]   LEAK2 = %s\n", overall_leak_detected ? "true" : "false");
    MOOSTrace("[LEAK]   LEAK2_CODE = %.0f\n", leak_code);
    MOOSTrace("[LEAK]   LEAK2_SENSORS = %s\n", m_leak_detected ? "true" : "false");
    MOOSTrace("[LEAK]   LEAK2_TAIL = %.0f\n", m_leak1_status);

    MOOSTrace("[LEAK] 综合漏水状态发布: 总体=%s, 故障码=%.0f, 传感器=%s, 尾端=%.0f\n",
              overall_leak_detected ? "LEAK_DETECTED" : "ALL_OK",
              leak_code,
              m_leak_detected ? "LEAK" : "OK",
              m_leak1_status);
}

// 处理抛载控制动作 - 已删除，只保留漏水检测功能
/*
void LEAK2::HandleLoadAction(bool action)
{
    MOOSTrace("[LEAK] 处理抛载控制: %s\n", action ? "开启" : "关闭");

    // 设置设备控制数组中的抛载控制位
    // 根据实际需求设置相应的控制字节
    if (action)
    {
        m_device_control[0] |= 0x01; // 设置抛载控制位
    }
    else
    {
        m_device_control[0] &= ~0x01; // 清除抛载控制位
    }

    // 标记控制帧准备发送
    m_control_frame_ready = true;
}

// 处理控制消息
void LEAK2::HandleControlMessage(string msg)
{
    MOOSTrace("[LEAK] 处理控制消息: %s\n", msg.c_str());

    // 解析控制消息格式，例如: "MSGTYPE=CONTROL,ACT=SENSOR,TYPE=LEAK,POWER=ON"
    vector<string> params;
    string delimiter = ",";
    size_t pos = 0;
    string token;
    string temp_msg = msg;

    while ((pos = temp_msg.find(delimiter)) != string::npos)
    {
        token = temp_msg.substr(0, pos);
        params.push_back(token);
        temp_msg.erase(0, pos + delimiter.length());
    }
    params.push_back(temp_msg); // 添加最后一个参数

    // 解析参数
    string msgType, act, type, power;
    for (const string &p : params)
    {
        size_t eq_pos = p.find('=');
        if (eq_pos != string::npos)
        {
            string key = p.substr(0, eq_pos);
            string value = p.substr(eq_pos + 1);

            // 去除空格并转换为大写 - 使用MOOS工具函数
            MOOSTrimWhiteSpace(key);
            MOOSToUpper(key);
            MOOSTrimWhiteSpace(value);
            MOOSToUpper(value);

            if (key == "MSGTYPE")
                msgType = value;
            else if (key == "ACT")
                act = value;
            else if (key == "TYPE")
                type = value;
            else if (key == "POWER")
                power = value;
        }
    }

    // 验证消息格式
    if (msgType != "CONTROL" || act != "SENSOR")
    {
        MOOSTrace("[LEAK] 无效的控制消息格式: %s\n", msg.c_str());
        return;
    }

    // LEAK2模块只处理漏水传感器相关的控制消息
    MOOSTrace("[LEAK2] 已接收设备控制消息，等待进一步处理: %s\n", msg.c_str());
}
*/


//---------------------------------------------------------
// Procedure: OnStartUp()
//            happens before connection is open
