/************************************************************/
/*    NAME: zhaoqinchao                                    */
/*    ORGN: HEU                                             */
/*    FILE: LEAK2.h                                        */
/*    DATE: 2025/07/18                                      */
/************************************************************/

#ifndef LEAK2_HEADER
#define LEAK2_HEADER

#include "MOOS/libMOOS/MOOSLib.h"
#include "MOOS/libMOOS/Thirdparty/AppCasting/AppCastingMOOSApp.h"
#include "MBUtils.h"
#include "BlueSocket.h"
#include  "unistd.h"

const size_t FRAME_LEN = 13;

class LEAK2 : public AppCastingMOOSApp
{
public:
  LEAK2();
  ~LEAK2();
  bool OnNewMail(MOOSMSG_LIST &NewMail);
  bool Iterate();
  bool OnConnectToServer();
  bool OnStartUp();
  void RegisterVariables();
  void RecvFrame();

private:             // Configuration variables
  int m_max_retries; // 最大重试次数
  int m_retry_delay; // 重试延迟（秒）
  std::string m_recv_ip;   // 接收IP地址
  int m_recv_port;         // 接收端口
  std::string m_dest_ip;   // 目标IP地址
  int m_dest_port;         // 目标端口

  void ParseFrame(std::vector<uint8_t> Frame);
  void ParseLeakageFrame(std::vector<uint8_t> Frame);
  // void ParseControlFrame(std::vector<uint8_t> Frame);  // 解析控制帧 - 已删除
  void UpdateLocalMOOSVariables(MOOSMSG_LIST &NewMail);  // 更新本地MOOS变量
  void PublishLeak2Status();  // 发布LEAK2状态到MOOS
  void PublishCombinedLeakStatus();  // 发布综合漏水状态 - 本地传感器状态与LEAK1状态"或"运算后发布
  void SendFrame(std::vector<uint8_t> Frame);
  // void SendControlFrame();  // 发送控制帧到CAN网络 - 已删除
  void ForwardCANFrame(uint8_t* sensor_data);  // 转发CAN数据帧
  void ParseSensorStatus(uint8_t* sensor_data);  // 解析传感器状态
  // void HandleControlMessage(std::string msg);  // 处理CONTROL_MSG - 已删除
  // void HandleLoadAction(bool action);  // 处理LOAD_ACTION - 已删除
  static void* RecvFrameWrapper(void* arg);  // 静态线程包装函数声明
  class BlueSocket RecvSock;
  class BlueSocket SendSock;

private: // State variables
  unsigned int m_iterations;
  double m_timewarp;

  // 线程控制变量
  bool m_thread_running;   // 线程运行标志
  pthread_t m_recv_thread; // 接收线程句柄

  // 漏水检测相关变量
  bool m_leak_detected;          // 总体漏水状态
  bool m_sensor_status[4];       // 4个传感器状态 (true=漏水, false=正常) - 适配32程序
  uint8_t m_leak_checksum;       // 接收到的校验和
  uint8_t m_calculated_checksum; // 计算得出的校验和

  // LEAK1变量相关 - 来自iThruster_HEU模块的尾端漏水检测
  double m_leak1_status;         // LEAK1变量值 (0=正常, 64=尾端漏水故障)

  // 设备控制相关变量 - 已删除，只保留漏水检测功能
  /*
  bool m_load_action;            // LOAD_ACTION变量值 (true=开启, false=关闭)
  std::string m_control_msg;     // CONTROL_MSG变量值
  uint8_t m_device_control[10];  // 设备控制数组，对应iCAN0_HEU的data1数组
  bool m_control_frame_ready;    // 控制帧准备发送标志
  */
};

#endif
