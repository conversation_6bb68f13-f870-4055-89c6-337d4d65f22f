/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    can.c
  * @brief   This file provides code for the configuration
  *          of the CAN instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "can.h"

/* USER CODE BEGIN 0 */
bool status_changed=false;
/* USER CODE END 0 */

CAN_HandleTypeDef hcan1;

/* CAN1 init function */
void MX_CAN1_Init(void)
{

  /* USER CODE BEGIN CAN1_Init 0 */

  /* USER CODE END CAN1_Init 0 */

  /* USER CODE BEGIN CAN1_Init 1 */

  /* USER CODE END CAN1_Init 1 */
  hcan1.Instance = CAN1;
  hcan1.Init.Prescaler = 24;
  hcan1.Init.Mode = CAN_MODE_NORMAL;
  hcan1.Init.SyncJumpWidth = CAN_SJW_1TQ;
  hcan1.Init.TimeSeg1 = CAN_BS1_3TQ;
  hcan1.Init.TimeSeg2 = CAN_BS2_2TQ;
  hcan1.Init.TimeTriggeredMode = DISABLE;
  hcan1.Init.AutoBusOff = DISABLE;
  hcan1.Init.AutoWakeUp = DISABLE;
  hcan1.Init.AutoRetransmission = ENABLE;
  hcan1.Init.ReceiveFifoLocked = DISABLE;
  hcan1.Init.TransmitFifoPriority = DISABLE;
  if (HAL_CAN_Init(&hcan1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN CAN1_Init 2 */

  /* USER CODE END CAN1_Init 2 */

}

void HAL_CAN_MspInit(CAN_HandleTypeDef* canHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(canHandle->Instance==CAN1)
  {
  /* USER CODE BEGIN CAN1_MspInit 0 */

  /* USER CODE END CAN1_MspInit 0 */
    /* CAN1 clock enable */
    __HAL_RCC_CAN1_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**CAN1 GPIO Configuration
    PA11     ------> CAN1_RX
    PA12     ------> CAN1_TX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_11|GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF9_CAN1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* CAN1 interrupt Init */
    HAL_NVIC_SetPriority(CAN1_RX0_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(CAN1_RX0_IRQn);
    HAL_NVIC_SetPriority(CAN1_RX1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(CAN1_RX1_IRQn);
  /* USER CODE BEGIN CAN1_MspInit 1 */

  /* USER CODE END CAN1_MspInit 1 */
  }
}

void HAL_CAN_MspDeInit(CAN_HandleTypeDef* canHandle)
{

  if(canHandle->Instance==CAN1)
  {
  /* USER CODE BEGIN CAN1_MspDeInit 0 */

  /* USER CODE END CAN1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_CAN1_CLK_DISABLE();

    /**CAN1 GPIO Configuration
    PA11     ------> CAN1_RX
    PA12     ------> CAN1_TX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_11|GPIO_PIN_12);

    /* CAN1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(CAN1_RX0_IRQn);
    HAL_NVIC_DisableIRQ(CAN1_RX1_IRQn);
  /* USER CODE BEGIN CAN1_MspDeInit 1 */

  /* USER CODE END CAN1_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
//------------------CAN1配置--------------------------------//
//------------------CAN1配置--------------------------------//
void CAN1_Config(void)
{	
 
	CAN_FilterTypeDef  sFilterConfig;
 
  /*配置CAN过滤器*/
  sFilterConfig.FilterBank = 0;                     //过滤器0
  sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;
  sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;
  sFilterConfig.FilterIdHigh = 0x0000;              //32位ID
  sFilterConfig.FilterIdLow = 0x0000;
  sFilterConfig.FilterMaskIdHigh = 0x0000;          //32位MASK
  sFilterConfig.FilterMaskIdLow = 0x0000;
  sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;//过滤器0关联到FIFO0
  sFilterConfig.FilterActivation = ENABLE;          //激活滤波器0
  sFilterConfig.SlaveStartFilterBank = 14;
	
	
 if(HAL_CAN_ConfigFilter(&hcan1,&sFilterConfig) != HAL_OK)//初始化过滤器
 {
  Error_Handler();
 }
 if(HAL_CAN_Start(&hcan1) != HAL_OK)//打开can
 {
  Error_Handler();
 }
 if(HAL_CAN_ActivateNotification(&hcan1,CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)//开启接受邮邮箱0挂起中断
 {
  Error_Handler();
 }
 
}
//-------------------------------CAN中断回调-----------------------------------//
// 接收：CAN接收 -> 中断触发 -> 消息读取 -> 数据处理
// 发送：数据准备 -> 发送请求 -> CAN消息发送 -> 发送完成处理
    // 读取CAN消息并处理
    // 发送CAN消息
    // 发送失败处理
    // 等待发送完成
    // 检测泄漏
void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan)
{
    if (hcan->Instance == CAN1)
  {   
        CAN_RxHeaderTypeDef rxHeader;
        uint8_t data[8];
        if (HAL_CAN_GetRxMessage(hcan, CAN_RX_FIFO0, &rxHeader, data) == HAL_OK)
        {
            // 读取CAN消息并处理  
            // 发送CAN消息
            // 发送失败处理
            // 等待发送完成
            // 检测泄漏
        }
    }
}

void CAN_SendMessage(uint32_t id, uint8_t *data, uint8_t len)
{
    CAN_TxHeaderTypeDef txMessage;
    uint32_t mailbox;
    HAL_StatusTypeDef status;
    txMessage.StdId = id;
    txMessage.ExtId = 0;
    txMessage.RTR = CAN_RTR_DATA;
    txMessage.IDE = CAN_ID_STD;
    if (len > 8) {
        len = 8;
    }
    txMessage.DLC = len;
    //  发送消息队列函数
    status = HAL_CAN_AddTxMessage(&hcan1, &txMessage, data, &mailbox);
    if (status != HAL_OK)
    {
        (void)HAL_CAN_GetError(&hcan1);
        Error_Handler();
    }

    // 带超时的等待，避免因无ACK导致长时间阻塞
    uint32_t startTick = HAL_GetTick();
    while (HAL_CAN_GetTxMailboxesFreeLevel(&hcan1) != 3) {
        if ((HAL_GetTick() - startTick) > 10) {
            break;
        }
    }
}


void Leakage_Detection(void)
{
    static uint8_t Leakage_Detection_data[8] = {0xFB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    const uint16_t DEC_PINS[4] = {GPIO_PIN_0, GPIO_PIN_1, GPIO_PIN_2, GPIO_PIN_3};
    const uint8_t PIN_VALUES[4] = {0x01, 0x02, 0x04, 0x08};  //漏水上报的报文数值
    // 重置状态变量
    status_changed = false;

    // 循环去判断GPIO口是否漏水
    for(int i = 0; i < 4; i++) {
        if(HAL_GPIO_ReadPin(GPIOA, DEC_PINS[i]) == RESET) {  // 检测到漏水
            Leakage_Detection_data[i + 1] = PIN_VALUES[i];
            status_changed = true;
        } else {
            Leakage_Detection_data[i + 1] = 0x00;
        }
    }

    // 计算校验和
    uint8_t checksum = 0x00;
    for(int i = 1; i <= 4; i++) {
        checksum += Leakage_Detection_data[i];
    }
    Leakage_Detection_data[7] = checksum;

    // 发送CAN报文 (每次都发送，便于调试)
    CAN_SendMessage(0x321, Leakage_Detection_data, 8);

    HAL_Delay(100);
}

/* USER CODE END 1 */
