T3220 000:092.913   SEGGER J-Link V6.98e Log File
T3220 000:093.120   DLL Compiled: Mar 29 2021 14:18:39
T3220 000:093.157   Logging started @ 2025-08-08 02:12
T3220 000:093.193 - 93.221ms
T3220 000:093.246 JLINK_SetWarnOutHandler(...)
T3220 000:093.281 - 0.052ms
T3220 000:093.482 JLINK_OpenEx(...)
T3220 000:095.227   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T3220 000:095.849   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T3220 000:098.360   Hardware: V7.00
T3220 000:098.427   S/N: 20090929
T3220 000:098.468   OEM: SEGGER
T3220 000:098.508   Feature(s): RDI,FlashDL,FlashB<PERSON>,JFlash,GDBFull
T3220 000:099.722   TELNET listener socket opened on port 19021
T3220 000:099.888   WEBSRV Starting webserver
T3220 000:100.108   WEBSRV Webserver running on local port 19080
T3220 000:100.155 - 6.688ms returns "O.K."
T3220 000:100.205 JLINK_GetEmuCaps()
T3220 000:100.237 - 0.047ms returns 0x88EA5833
T3220 000:100.274 JLINK_TIF_GetAvailable(...)
T3220 000:100.479 - 0.240ms
T3220 000:100.546 JLINK_SetErrorOutHandler(...)
T3220 000:100.577 - 0.045ms
T3220 000:100.622 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\BSP_ILEAK_HEU\MDK-ARM\JLinkSettings.ini"", ...). 
T3220 000:111.004   Ref file found at: E:\keil\ARM\Segger\JLinkDevices.ref
T3220 000:111.173   XML referenced by ref file: C:\Program Files (x86)\SEGGER\JLink\JLinkDevices.xml
T3220 000:111.793   C:\Program Files (x86)\SEGGER\JLink\JLinkDevices.xml evaluated successfully.
T3220 000:149.873 - 49.267ms returns 0x00
T3220 000:150.650 JLINK_ExecCommand("Device = STM32F407ZETx", ...). 
T3220 000:151.717   Device "STM32F407ZE" selected.
T3220 000:152.019 - 1.355ms returns 0x00
T3220 000:152.044 JLINK_GetHardwareVersion()
T3220 000:152.058 - 0.021ms returns 70000
T3220 000:152.073 JLINK_GetDLLVersion()
T3220 000:152.087 - 0.020ms returns 69805
T3220 000:152.102 JLINK_GetOEMString(...)
T3220 000:152.147 JLINK_GetFirmwareString(...)
T3220 000:152.163 - 0.023ms
T3220 000:154.229 JLINK_GetDLLVersion()
T3220 000:154.252 - 0.030ms returns 69805
T3220 000:154.268 JLINK_GetCompileDateTime()
T3220 000:154.282 - 0.020ms
T3220 000:154.849 JLINK_GetFirmwareString(...)
T3220 000:154.865 - 0.022ms
T3220 000:155.415 JLINK_GetHardwareVersion()
T3220 000:155.430 - 0.022ms returns 70000
T3220 000:155.968 JLINK_GetSN()
T3220 000:155.983 - 0.021ms returns 20090929
T3220 000:156.518 JLINK_GetOEMString(...)
T3220 000:160.957 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T3220 000:161.365 - 0.418ms returns 0x00
T3220 000:161.387 JLINK_HasError()
T3220 000:161.409 JLINK_SetSpeed(5000)
T3220 000:161.487 - 0.087ms
T3220 000:161.508 JLINK_GetId()
T3220 000:164.378   Found SW-DP with ID 0x2BA01477
T3220 000:180.807   Found SW-DP with ID 0x2BA01477
T3220 000:184.715   Old FW that does not support reading DPIDR via DAP jobs
T3220 000:191.421   DPv0 detected
T3220 000:194.140   Scanning AP map to find all available APs
T3220 000:198.724   AP[1]: Stopped AP scan as end of AP map has been reached
T3220 000:200.932   AP[0]: AHB-AP (IDR: 0x24770011)
T3220 000:203.691   Iterating through AP map to find AHB-AP to use
T3220 000:210.215   AP[0]: Core found
T3220 000:213.385   AP[0]: AHB-AP ROM base: 0xE00FF000
T3220 000:219.285   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T3220 000:222.287   Found Cortex-M4 r0p1, Little endian.
T3220 000:334.000   -- Max. mem block: 0x00002C18
T3220 000:334.125   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3220 000:334.591   CPU_ReadMem(4 bytes @ 0xE0002000)
T3220 000:337.541   FPUnit: 6 code (BP) slots and 2 literal slots
T3220 000:337.571   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3220 000:337.999   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3220 000:338.447   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:338.877   CPU_WriteMem(4 bytes @ 0xE0001000)
T3220 000:339.296   CPU_ReadMem(4 bytes @ 0xE000ED88)
T3220 000:339.688   CPU_WriteMem(4 bytes @ 0xE000ED88)
T3220 000:340.114   CPU_ReadMem(4 bytes @ 0xE000ED88)
T3220 000:340.537   CPU_WriteMem(4 bytes @ 0xE000ED88)
T3220 000:343.560   CoreSight components:
T3220 000:345.410   ROMTbl[0] @ E00FF000
T3220 000:345.440   CPU_ReadMem(64 bytes @ 0xE00FF000)
T3220 000:346.461   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T3220 000:348.832   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T3220 000:348.862   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T3220 000:351.427   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T3220 000:351.456   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T3220 000:354.111   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T3220 000:354.137   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T3220 000:356.730   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T3220 000:356.758   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T3220 000:359.216   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T3220 000:359.245   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T3220 000:361.674   ROMTbl[0][5]: ********, CID: B105900D, PID: 000BB925 ETM
T3220 000:362.027 - 200.532ms returns 0x2BA01477
T3220 000:362.054 JLINK_GetDLLVersion()
T3220 000:362.067 - 0.018ms returns 69805
T3220 000:362.082 JLINK_CORE_GetFound()
T3220 000:362.095 - 0.019ms returns 0xE0000FF
T3220 000:362.113 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T3220 000:362.127   Value=0xE00FF000
T3220 000:362.145 - 0.037ms returns 0
T3220 000:369.010 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T3220 000:369.036   Value=0xE00FF000
T3220 000:369.055 - 0.053ms returns 0
T3220 000:369.071 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T3220 000:369.085   Value=0x********
T3220 000:369.103 - 0.038ms returns 0
T3220 000:369.118 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T3220 000:369.146   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T3220 000:369.882   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T3220 000:369.911 - 0.799ms returns 32 (0x20)
T3220 000:369.927 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T3220 000:369.940   Value=0x00000000
T3220 000:369.958 - 0.036ms returns 0
T3220 000:369.971 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T3220 000:369.983   Value=0x********
T3220 000:370.002 - 0.036ms returns 0
T3220 000:370.015 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T3220 000:370.028   Value=0x********
T3220 000:370.050 - 0.041ms returns 0
T3220 000:370.064 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T3220 000:370.077   Value=0xE0001000
T3220 000:370.094 - 0.037ms returns 0
T3220 000:370.108 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T3220 000:370.120   Value=0xE0002000
T3220 000:370.139 - 0.037ms returns 0
T3220 000:370.153 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T3220 000:370.165   Value=0xE000E000
T3220 000:370.183 - 0.036ms returns 0
T3220 000:370.196 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T3220 000:370.210   Value=0xE000EDF0
T3220 000:370.232 - 0.042ms returns 0
T3220 000:370.245 JLINK_GetDebugInfo(0x01 = Unknown)
T3220 000:370.259   Value=0x00000001
T3220 000:370.277 - 0.037ms returns 0
T3220 000:370.290 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T3220 000:370.308   CPU_ReadMem(4 bytes @ 0xE000ED00)
T3220 000:370.694   Data:  41 C2 0F 41
T3220 000:370.722   Debug reg: CPUID
T3220 000:370.740 - 0.455ms returns 1 (0x1)
T3220 000:370.756 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T3220 000:370.769   Value=0x00000000
T3220 000:370.786 - 0.036ms returns 0
T3220 000:370.800 JLINK_HasError()
T3220 000:370.815 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T3220 000:370.828 - 0.019ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T3220 000:370.841 JLINK_Reset()
T3220 000:370.864   CPU is running
T3220 000:370.883   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3220 000:371.375   CPU is running
T3220 000:371.408   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3220 000:373.725   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T3220 000:376.180   Reset: Reset device via AIRCR.SYSRESETREQ.
T3220 000:376.210   CPU is running
T3220 000:376.231   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T3220 000:443.216   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3220 000:443.628   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3220 000:444.042   CPU is running
T3220 000:444.065   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3220 000:444.482   CPU is running
T3220 000:444.507   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3220 000:457.849   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3220 000:461.356   CPU_WriteMem(4 bytes @ 0xE0002000)
T3220 000:461.816   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3220 000:462.328   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:462.736 - 91.913ms
T3220 000:462.770 JLINK_Halt()
T3220 000:462.782 - 0.018ms returns 0x00
T3220 000:462.835 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T3220 000:462.853   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3220 000:463.305   Data:  03 00 03 00
T3220 000:463.323   Debug reg: DHCSR
T3220 000:463.339 - 0.509ms returns 1 (0x1)
T3220 000:463.351 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T3220 000:463.365   Debug reg: DHCSR
T3220 000:463.561   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3220 000:463.987 - 0.649ms returns 0 (0x00000000)
T3220 000:464.010 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T3220 000:464.025   Debug reg: DEMCR
T3220 000:464.048   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3220 000:464.461 - 0.464ms returns 0 (0x00000000)
T3220 000:471.308 JLINK_GetHWStatus(...)
T3220 000:471.503 - 0.207ms returns 0
T3220 000:476.374 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T3220 000:476.396 - 0.027ms returns 0x06
T3220 000:476.409 JLINK_GetNumBPUnits(Type = 0xF0)
T3220 000:476.420 - 0.017ms returns 0x2000
T3220 000:476.432 JLINK_GetNumWPUnits()
T3220 000:476.443 - 0.016ms returns 4
T3220 000:481.968 JLINK_GetSpeed()
T3220 000:481.989 - 0.026ms returns 4000
T3220 000:485.789 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T3220 000:485.825   CPU_ReadMem(4 bytes @ 0xE000E004)
T3220 000:486.242   Data:  02 00 00 00
T3220 000:486.268 - 0.486ms returns 1 (0x1)
T3220 000:486.284 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T3220 000:486.301   CPU_ReadMem(4 bytes @ 0xE000E004)
T3220 000:486.692   Data:  02 00 00 00
T3220 000:486.750 - 0.471ms returns 1 (0x1)
T3220 000:486.764 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T3220 000:486.776   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T3220 000:486.798   CPU_WriteMem(28 bytes @ 0xE0001000)
T3220 000:487.525 - 0.793ms returns 0x1C
T3220 000:487.570 JLINK_Halt()
T3220 000:487.582 - 0.018ms returns 0x00
T3220 000:487.594 JLINK_IsHalted()
T3220 000:487.610 - 0.021ms returns TRUE
T3220 000:490.224 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T3220 000:490.241   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T3220 000:490.434   CPU_WriteMem(388 bytes @ 0x20000000)
T3220 000:495.407 - 5.197ms returns 0x184
T3220 000:495.466 JLINK_HasError()
T3220 000:495.480 JLINK_WriteReg(R0, 0x08000000)
T3220 000:495.499 - 0.024ms returns 0
T3220 000:495.512 JLINK_WriteReg(R1, 0x017D7840)
T3220 000:495.524 - 0.018ms returns 0
T3220 000:495.537 JLINK_WriteReg(R2, 0x00000001)
T3220 000:495.549 - 0.017ms returns 0
T3220 000:495.562 JLINK_WriteReg(R3, 0x00000000)
T3220 000:495.574 - 0.018ms returns 0
T3220 000:495.586 JLINK_WriteReg(R4, 0x00000000)
T3220 000:495.598 - 0.017ms returns 0
T3220 000:495.611 JLINK_WriteReg(R5, 0x00000000)
T3220 000:495.623 - 0.017ms returns 0
T3220 000:495.636 JLINK_WriteReg(R6, 0x00000000)
T3220 000:495.648 - 0.017ms returns 0
T3220 000:495.660 JLINK_WriteReg(R7, 0x00000000)
T3220 000:495.672 - 0.017ms returns 0
T3220 000:495.685 JLINK_WriteReg(R8, 0x00000000)
T3220 000:495.699 - 0.020ms returns 0
T3220 000:495.712 JLINK_WriteReg(R9, 0x20000180)
T3220 000:495.724 - 0.017ms returns 0
T3220 000:495.736 JLINK_WriteReg(R10, 0x00000000)
T3220 000:495.748 - 0.017ms returns 0
T3220 000:495.761 JLINK_WriteReg(R11, 0x00000000)
T3220 000:495.773 - 0.017ms returns 0
T3220 000:495.786 JLINK_WriteReg(R12, 0x00000000)
T3220 000:495.798 - 0.017ms returns 0
T3220 000:495.810 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 000:495.823 - 0.018ms returns 0
T3220 000:495.838 JLINK_WriteReg(R14, 0x20000001)
T3220 000:495.851 - 0.018ms returns 0
T3220 000:495.864 JLINK_WriteReg(R15 (PC), 0x20000054)
T3220 000:495.877 - 0.019ms returns 0
T3220 000:495.892 JLINK_WriteReg(XPSR, 0x01000000)
T3220 000:495.903 - 0.017ms returns 0
T3220 000:495.916 JLINK_WriteReg(MSP, 0x20001000)
T3220 000:495.927 - 0.017ms returns 0
T3220 000:495.940 JLINK_WriteReg(PSP, 0x20001000)
T3220 000:495.951 - 0.017ms returns 0
T3220 000:495.963 JLINK_WriteReg(CFBP, 0x00000000)
T3220 000:495.975 - 0.017ms returns 0
T3220 000:495.987 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 000:496.002   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:496.443 - 0.463ms returns 0x00000001
T3220 000:496.458 JLINK_Go()
T3220 000:496.471   CPU_WriteMem(2 bytes @ 0x20000000)
T3220 000:497.016   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:497.423   CPU_WriteMem(4 bytes @ 0xE0002008)
T3220 000:497.443   CPU_WriteMem(4 bytes @ 0xE000200C)
T3220 000:497.463   CPU_WriteMem(4 bytes @ 0xE0002010)
T3220 000:497.481   CPU_WriteMem(4 bytes @ 0xE0002014)
T3220 000:497.499   CPU_WriteMem(4 bytes @ 0xE0002018)
T3220 000:497.517   CPU_WriteMem(4 bytes @ 0xE000201C)
T3220 000:499.922   CPU_WriteMem(4 bytes @ 0xE0001004)
T3220 000:503.392 - 6.946ms
T3220 000:503.414 JLINK_IsHalted()
T3220 000:506.499   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:506.910 - 3.506ms returns TRUE
T3220 000:506.929 JLINK_ReadReg(R15 (PC))
T3220 000:506.944 - 0.019ms returns 0x20000000
T3220 000:506.956 JLINK_ClrBPEx(BPHandle = 0x00000001)
T3220 000:506.968 - 0.017ms returns 0x00
T3220 000:506.981 JLINK_ReadReg(R0)
T3220 000:506.992 - 0.017ms returns 0x00000000
T3220 000:507.267 JLINK_HasError()
T3220 000:507.284 JLINK_WriteReg(R0, 0x08000000)
T3220 000:507.298 - 0.018ms returns 0
T3220 000:507.310 JLINK_WriteReg(R1, 0x00004000)
T3220 000:507.333 - 0.028ms returns 0
T3220 000:507.346 JLINK_WriteReg(R2, 0x000000FF)
T3220 000:507.358 - 0.018ms returns 0
T3220 000:507.371 JLINK_WriteReg(R3, 0x00000000)
T3220 000:507.382 - 0.017ms returns 0
T3220 000:507.395 JLINK_WriteReg(R4, 0x00000000)
T3220 000:507.407 - 0.017ms returns 0
T3220 000:507.419 JLINK_WriteReg(R5, 0x00000000)
T3220 000:507.431 - 0.017ms returns 0
T3220 000:507.443 JLINK_WriteReg(R6, 0x00000000)
T3220 000:507.454 - 0.017ms returns 0
T3220 000:507.467 JLINK_WriteReg(R7, 0x00000000)
T3220 000:507.478 - 0.017ms returns 0
T3220 000:507.490 JLINK_WriteReg(R8, 0x00000000)
T3220 000:507.501 - 0.017ms returns 0
T3220 000:507.514 JLINK_WriteReg(R9, 0x20000180)
T3220 000:507.526 - 0.018ms returns 0
T3220 000:507.539 JLINK_WriteReg(R10, 0x00000000)
T3220 000:507.551 - 0.017ms returns 0
T3220 000:507.563 JLINK_WriteReg(R11, 0x00000000)
T3220 000:507.575 - 0.017ms returns 0
T3220 000:507.587 JLINK_WriteReg(R12, 0x00000000)
T3220 000:507.598 - 0.017ms returns 0
T3220 000:507.611 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 000:507.622 - 0.017ms returns 0
T3220 000:507.634 JLINK_WriteReg(R14, 0x20000001)
T3220 000:507.646 - 0.016ms returns 0
T3220 000:507.658 JLINK_WriteReg(R15 (PC), 0x20000020)
T3220 000:507.669 - 0.017ms returns 0
T3220 000:507.681 JLINK_WriteReg(XPSR, 0x01000000)
T3220 000:507.693 - 0.017ms returns 0
T3220 000:507.705 JLINK_WriteReg(MSP, 0x20001000)
T3220 000:507.716 - 0.016ms returns 0
T3220 000:507.728 JLINK_WriteReg(PSP, 0x20001000)
T3220 000:507.740 - 0.016ms returns 0
T3220 000:507.752 JLINK_WriteReg(CFBP, 0x00000000)
T3220 000:507.763 - 0.017ms returns 0
T3220 000:507.775 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 000:507.787 - 0.017ms returns 0x00000002
T3220 000:507.800 JLINK_Go()
T3220 000:507.816   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:511.167 - 3.382ms
T3220 000:511.191 JLINK_IsHalted()
T3220 000:514.280   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:514.675 - 3.496ms returns TRUE
T3220 000:514.697 JLINK_ReadReg(R15 (PC))
T3220 000:514.711 - 0.020ms returns 0x20000000
T3220 000:514.724 JLINK_ClrBPEx(BPHandle = 0x00000002)
T3220 000:514.736 - 0.017ms returns 0x00
T3220 000:514.777 JLINK_ReadReg(R0)
T3220 000:514.791 - 0.019ms returns 0x00000001
T3220 000:514.804 JLINK_HasError()
T3220 000:514.817 JLINK_WriteReg(R0, 0x08000000)
T3220 000:514.829 - 0.017ms returns 0
T3220 000:514.841 JLINK_WriteReg(R1, 0x00004000)
T3220 000:514.889 - 0.053ms returns 0
T3220 000:514.901 JLINK_WriteReg(R2, 0x000000FF)
T3220 000:514.913 - 0.017ms returns 0
T3220 000:514.925 JLINK_WriteReg(R3, 0x00000000)
T3220 000:514.937 - 0.017ms returns 0
T3220 000:514.949 JLINK_WriteReg(R4, 0x00000000)
T3220 000:514.961 - 0.017ms returns 0
T3220 000:514.973 JLINK_WriteReg(R5, 0x00000000)
T3220 000:514.985 - 0.017ms returns 0
T3220 000:514.997 JLINK_WriteReg(R6, 0x00000000)
T3220 000:515.008 - 0.017ms returns 0
T3220 000:515.021 JLINK_WriteReg(R7, 0x00000000)
T3220 000:515.032 - 0.017ms returns 0
T3220 000:515.045 JLINK_WriteReg(R8, 0x00000000)
T3220 000:515.056 - 0.017ms returns 0
T3220 000:515.068 JLINK_WriteReg(R9, 0x20000180)
T3220 000:515.080 - 0.017ms returns 0
T3220 000:515.092 JLINK_WriteReg(R10, 0x00000000)
T3220 000:515.104 - 0.017ms returns 0
T3220 000:515.116 JLINK_WriteReg(R11, 0x00000000)
T3220 000:515.128 - 0.017ms returns 0
T3220 000:515.140 JLINK_WriteReg(R12, 0x00000000)
T3220 000:515.152 - 0.017ms returns 0
T3220 000:515.164 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 000:515.176 - 0.017ms returns 0
T3220 000:515.188 JLINK_WriteReg(R14, 0x20000001)
T3220 000:515.204 - 0.021ms returns 0
T3220 000:515.217 JLINK_WriteReg(R15 (PC), 0x200000C0)
T3220 000:515.228 - 0.017ms returns 0
T3220 000:515.241 JLINK_WriteReg(XPSR, 0x01000000)
T3220 000:515.253 - 0.017ms returns 0
T3220 000:515.265 JLINK_WriteReg(MSP, 0x20001000)
T3220 000:515.276 - 0.017ms returns 0
T3220 000:515.289 JLINK_WriteReg(PSP, 0x20001000)
T3220 000:515.300 - 0.017ms returns 0
T3220 000:515.313 JLINK_WriteReg(CFBP, 0x00000000)
T3220 000:515.324 - 0.017ms returns 0
T3220 000:515.337 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 000:515.349 - 0.018ms returns 0x00000003
T3220 000:515.362 JLINK_Go()
T3220 000:515.378   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:518.932 - 3.584ms
T3220 000:518.955 JLINK_IsHalted()
T3220 000:519.326 - 0.378ms returns FALSE
T3220 000:519.342 JLINK_HasError()
T3220 000:535.664 JLINK_IsHalted()
T3220 000:536.091 - 0.441ms returns FALSE
T3220 000:536.116 JLINK_HasError()
T3220 000:550.752 JLINK_IsHalted()
T3220 000:551.220 - 0.478ms returns FALSE
T3220 000:551.242 JLINK_HasError()
T3220 000:565.893 JLINK_IsHalted()
T3220 000:566.319 - 0.447ms returns FALSE
T3220 000:566.352 JLINK_HasError()
T3220 000:581.101 JLINK_IsHalted()
T3220 000:581.728 - 0.654ms returns FALSE
T3220 000:581.778 JLINK_HasError()
T3220 000:596.811 JLINK_IsHalted()
T3220 000:597.357 - 0.594ms returns FALSE
T3220 000:597.437 JLINK_HasError()
T3220 000:611.905 JLINK_IsHalted()
T3220 000:612.427 - 0.545ms returns FALSE
T3220 000:612.485 JLINK_HasError()
T3220 000:627.602 JLINK_IsHalted()
T3220 000:628.116 - 0.558ms returns FALSE
T3220 000:628.190 JLINK_HasError()
T3220 000:642.978 JLINK_IsHalted()
T3220 000:643.488 - 0.549ms returns FALSE
T3220 000:643.555 JLINK_HasError()
T3220 000:658.141 JLINK_IsHalted()
T3220 000:658.686 - 0.576ms returns FALSE
T3220 000:658.745 JLINK_HasError()
T3220 000:673.712 JLINK_IsHalted()
T3220 000:674.309 - 0.619ms returns FALSE
T3220 000:674.370 JLINK_HasError()
T3220 000:689.666 JLINK_IsHalted()
T3220 000:690.308 - 0.681ms returns FALSE
T3220 000:690.374 JLINK_HasError()
T3220 000:704.930 JLINK_IsHalted()
T3220 000:705.541 - 0.635ms returns FALSE
T3220 000:705.593 JLINK_HasError()
T3220 000:720.120 JLINK_IsHalted()
T3220 000:720.926 - 0.823ms returns FALSE
T3220 000:720.953 JLINK_HasError()
T3220 000:735.679 JLINK_IsHalted()
T3220 000:736.115 - 0.446ms returns FALSE
T3220 000:736.136 JLINK_HasError()
T3220 000:750.781 JLINK_IsHalted()
T3220 000:751.214 - 0.481ms returns FALSE
T3220 000:751.271 JLINK_HasError()
T3220 000:766.251 JLINK_IsHalted()
T3220 000:766.830 - 0.606ms returns FALSE
T3220 000:766.867 JLINK_HasError()
T3220 000:781.926 JLINK_IsHalted()
T3220 000:782.496 - 0.597ms returns FALSE
T3220 000:782.542 JLINK_HasError()
T3220 000:797.752 JLINK_IsHalted()
T3220 000:798.466 - 0.756ms returns FALSE
T3220 000:798.536 JLINK_HasError()
T3220 000:812.942 JLINK_IsHalted()
T3220 000:813.546 - 0.643ms returns FALSE
T3220 000:813.613 JLINK_HasError()
T3220 000:828.156 JLINK_IsHalted()
T3220 000:828.804 - 0.697ms returns FALSE
T3220 000:828.879 JLINK_HasError()
T3220 000:843.260 JLINK_IsHalted()
T3220 000:843.796 - 0.578ms returns FALSE
T3220 000:843.866 JLINK_HasError()
T3220 000:859.242 JLINK_IsHalted()
T3220 000:862.614   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:863.299 - 4.077ms returns TRUE
T3220 000:863.344 JLINK_ReadReg(R15 (PC))
T3220 000:863.384 - 0.057ms returns 0x20000000
T3220 000:863.422 JLINK_ClrBPEx(BPHandle = 0x00000003)
T3220 000:863.458 - 0.053ms returns 0x00
T3220 000:863.495 JLINK_ReadReg(R0)
T3220 000:863.531 - 0.052ms returns 0x00000000
T3220 000:864.221 JLINK_HasError()
T3220 000:864.270 JLINK_WriteReg(R0, 0x00000001)
T3220 000:864.309 - 0.056ms returns 0
T3220 000:864.347 JLINK_WriteReg(R1, 0x00004000)
T3220 000:864.382 - 0.052ms returns 0
T3220 000:864.418 JLINK_WriteReg(R2, 0x000000FF)
T3220 000:864.453 - 0.051ms returns 0
T3220 000:864.490 JLINK_WriteReg(R3, 0x00000000)
T3220 000:864.525 - 0.051ms returns 0
T3220 000:864.562 JLINK_WriteReg(R4, 0x00000000)
T3220 000:864.596 - 0.051ms returns 0
T3220 000:864.633 JLINK_WriteReg(R5, 0x00000000)
T3220 000:864.668 - 0.051ms returns 0
T3220 000:864.704 JLINK_WriteReg(R6, 0x00000000)
T3220 000:864.739 - 0.051ms returns 0
T3220 000:864.776 JLINK_WriteReg(R7, 0x00000000)
T3220 000:864.811 - 0.052ms returns 0
T3220 000:864.848 JLINK_WriteReg(R8, 0x00000000)
T3220 000:864.882 - 0.051ms returns 0
T3220 000:864.919 JLINK_WriteReg(R9, 0x20000180)
T3220 000:864.954 - 0.052ms returns 0
T3220 000:864.991 JLINK_WriteReg(R10, 0x00000000)
T3220 000:865.026 - 0.052ms returns 0
T3220 000:865.063 JLINK_WriteReg(R11, 0x00000000)
T3220 000:865.098 - 0.051ms returns 0
T3220 000:865.134 JLINK_WriteReg(R12, 0x00000000)
T3220 000:865.169 - 0.052ms returns 0
T3220 000:865.206 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 000:865.242 - 0.053ms returns 0
T3220 000:865.279 JLINK_WriteReg(R14, 0x20000001)
T3220 000:865.314 - 0.051ms returns 0
T3220 000:865.351 JLINK_WriteReg(R15 (PC), 0x20000086)
T3220 000:865.386 - 0.052ms returns 0
T3220 000:865.422 JLINK_WriteReg(XPSR, 0x01000000)
T3220 000:865.457 - 0.052ms returns 0
T3220 000:865.494 JLINK_WriteReg(MSP, 0x20001000)
T3220 000:865.529 - 0.052ms returns 0
T3220 000:865.566 JLINK_WriteReg(PSP, 0x20001000)
T3220 000:865.600 - 0.051ms returns 0
T3220 000:865.637 JLINK_WriteReg(CFBP, 0x00000000)
T3220 000:865.672 - 0.051ms returns 0
T3220 000:865.709 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 000:865.746 - 0.054ms returns 0x00000004
T3220 000:865.784 JLINK_Go()
T3220 000:865.831   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:869.690 - 3.950ms
T3220 000:869.760 JLINK_IsHalted()
T3220 000:873.175   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:873.786 - 4.069ms returns TRUE
T3220 000:873.857 JLINK_ReadReg(R15 (PC))
T3220 000:873.907 - 0.067ms returns 0x20000000
T3220 000:873.945 JLINK_ClrBPEx(BPHandle = 0x00000004)
T3220 000:873.982 - 0.054ms returns 0x00
T3220 000:874.020 JLINK_ReadReg(R0)
T3220 000:874.056 - 0.053ms returns 0x00000000
T3220 000:950.207 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T3220 000:950.262   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T3220 000:950.331   CPU_WriteMem(388 bytes @ 0x20000000)
T3220 000:955.456 - 5.296ms returns 0x184
T3220 000:955.615 JLINK_HasError()
T3220 000:955.658 JLINK_WriteReg(R0, 0x08000000)
T3220 000:955.700 - 0.059ms returns 0
T3220 000:955.738 JLINK_WriteReg(R1, 0x017D7840)
T3220 000:955.773 - 0.052ms returns 0
T3220 000:955.810 JLINK_WriteReg(R2, 0x00000002)
T3220 000:955.845 - 0.051ms returns 0
T3220 000:955.881 JLINK_WriteReg(R3, 0x00000000)
T3220 000:955.916 - 0.051ms returns 0
T3220 000:955.960 JLINK_WriteReg(R4, 0x00000000)
T3220 000:956.000 - 0.057ms returns 0
T3220 000:956.036 JLINK_WriteReg(R5, 0x00000000)
T3220 000:956.073 - 0.053ms returns 0
T3220 000:956.110 JLINK_WriteReg(R6, 0x00000000)
T3220 000:956.150 - 0.057ms returns 0
T3220 000:956.187 JLINK_WriteReg(R7, 0x00000000)
T3220 000:956.222 - 0.052ms returns 0
T3220 000:956.259 JLINK_WriteReg(R8, 0x00000000)
T3220 000:956.293 - 0.051ms returns 0
T3220 000:956.330 JLINK_WriteReg(R9, 0x20000180)
T3220 000:956.365 - 0.051ms returns 0
T3220 000:956.402 JLINK_WriteReg(R10, 0x00000000)
T3220 000:956.437 - 0.052ms returns 0
T3220 000:956.474 JLINK_WriteReg(R11, 0x00000000)
T3220 000:956.508 - 0.051ms returns 0
T3220 000:956.545 JLINK_WriteReg(R12, 0x00000000)
T3220 000:956.580 - 0.051ms returns 0
T3220 000:956.617 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 000:956.653 - 0.053ms returns 0
T3220 000:956.690 JLINK_WriteReg(R14, 0x20000001)
T3220 000:956.725 - 0.052ms returns 0
T3220 000:956.762 JLINK_WriteReg(R15 (PC), 0x20000054)
T3220 000:956.797 - 0.052ms returns 0
T3220 000:956.833 JLINK_WriteReg(XPSR, 0x01000000)
T3220 000:956.869 - 0.052ms returns 0
T3220 000:956.906 JLINK_WriteReg(MSP, 0x20001000)
T3220 000:956.940 - 0.051ms returns 0
T3220 000:956.977 JLINK_WriteReg(PSP, 0x20001000)
T3220 000:957.012 - 0.052ms returns 0
T3220 000:957.049 JLINK_WriteReg(CFBP, 0x00000000)
T3220 000:957.085 - 0.053ms returns 0
T3220 000:957.123 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 000:957.166   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:957.753 - 0.650ms returns 0x00000005
T3220 000:957.796 JLINK_Go()
T3220 000:957.835   CPU_WriteMem(2 bytes @ 0x20000000)
T3220 000:958.687   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:962.494 - 4.729ms
T3220 000:962.550 JLINK_IsHalted()
T3220 000:965.996   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 000:966.480 - 3.952ms returns TRUE
T3220 000:966.528 JLINK_ReadReg(R15 (PC))
T3220 000:966.568 - 0.057ms returns 0x20000000
T3220 000:966.605 JLINK_ClrBPEx(BPHandle = 0x00000005)
T3220 000:966.641 - 0.053ms returns 0x00
T3220 000:966.679 JLINK_ReadReg(R0)
T3220 000:966.714 - 0.052ms returns 0x00000000
T3220 000:970.511 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T3220 000:970.563   Data:  A8 06 00 20 29 02 00 08 F7 15 00 08 CD 14 00 08 ...
T3220 000:970.633   CPU_WriteMem(636 bytes @ 0x20000184)
T3220 000:978.719 - 8.251ms returns 0x27C
T3220 000:978.790 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T3220 000:978.826   Data:  B3 FA 83 F5 03 FA 05 F4 24 FA 05 F6 5E 40 12 BF ...
T3220 000:978.895   CPU_WriteMem(388 bytes @ 0x20000400)
T3220 000:984.011 - 5.256ms returns 0x184
T3220 000:984.079 JLINK_HasError()
T3220 000:984.166 JLINK_WriteReg(R0, 0x08000000)
T3220 000:984.209 - 0.060ms returns 0
T3220 000:984.246 JLINK_WriteReg(R1, 0x00000400)
T3220 000:984.282 - 0.053ms returns 0
T3220 000:984.319 JLINK_WriteReg(R2, 0x20000184)
T3220 000:984.354 - 0.052ms returns 0
T3220 000:984.391 JLINK_WriteReg(R3, 0x00000000)
T3220 000:984.426 - 0.052ms returns 0
T3220 000:984.463 JLINK_WriteReg(R4, 0x00000000)
T3220 000:984.498 - 0.051ms returns 0
T3220 000:984.535 JLINK_WriteReg(R5, 0x00000000)
T3220 000:984.569 - 0.051ms returns 0
T3220 000:984.606 JLINK_WriteReg(R6, 0x00000000)
T3220 000:984.641 - 0.051ms returns 0
T3220 000:984.678 JLINK_WriteReg(R7, 0x00000000)
T3220 000:984.712 - 0.051ms returns 0
T3220 000:984.749 JLINK_WriteReg(R8, 0x00000000)
T3220 000:984.784 - 0.052ms returns 0
T3220 000:984.821 JLINK_WriteReg(R9, 0x20000180)
T3220 000:984.856 - 0.052ms returns 0
T3220 000:984.893 JLINK_WriteReg(R10, 0x00000000)
T3220 000:984.927 - 0.052ms returns 0
T3220 000:984.964 JLINK_WriteReg(R11, 0x00000000)
T3220 000:984.999 - 0.051ms returns 0
T3220 000:985.036 JLINK_WriteReg(R12, 0x00000000)
T3220 000:985.071 - 0.052ms returns 0
T3220 000:985.108 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 000:985.144 - 0.053ms returns 0
T3220 000:985.180 JLINK_WriteReg(R14, 0x20000001)
T3220 000:985.215 - 0.051ms returns 0
T3220 000:985.252 JLINK_WriteReg(R15 (PC), 0x2000010C)
T3220 000:985.297 - 0.062ms returns 0
T3220 000:985.334 JLINK_WriteReg(XPSR, 0x01000000)
T3220 000:985.369 - 0.052ms returns 0
T3220 000:985.406 JLINK_WriteReg(MSP, 0x20001000)
T3220 000:985.440 - 0.051ms returns 0
T3220 000:985.477 JLINK_WriteReg(PSP, 0x20001000)
T3220 000:985.512 - 0.052ms returns 0
T3220 000:985.549 JLINK_WriteReg(CFBP, 0x00000000)
T3220 000:985.584 - 0.052ms returns 0
T3220 000:985.621 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 000:985.658 - 0.054ms returns 0x00000006
T3220 000:985.696 JLINK_Go()
T3220 000:985.742   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 000:989.517 - 3.845ms
T3220 000:989.561 JLINK_IsHalted()
T3220 000:990.038 - 0.525ms returns FALSE
T3220 000:990.114 JLINK_HasError()
T3220 000:996.354 JLINK_IsHalted()
T3220 000:999.817   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:000.341 - 4.034ms returns TRUE
T3220 001:000.415 JLINK_ReadReg(R15 (PC))
T3220 001:000.458 - 0.060ms returns 0x20000000
T3220 001:000.496 JLINK_ClrBPEx(BPHandle = 0x00000006)
T3220 001:000.532 - 0.053ms returns 0x00
T3220 001:000.570 JLINK_ReadReg(R0)
T3220 001:000.605 - 0.052ms returns 0x00000000
T3220 001:001.455 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T3220 001:001.499   Data:  10 40 FF F7 0A BF 00 00 00 48 70 47 44 00 00 20 ...
T3220 001:001.563   CPU_WriteMem(636 bytes @ 0x20000184)
T3220 001:009.414 - 7.999ms returns 0x27C
T3220 001:009.478 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T3220 001:009.513   Data:  C4 04 C4 F8 40 32 0C 89 CB 68 64 F3 1F 43 4C 69 ...
T3220 001:009.575   CPU_WriteMem(388 bytes @ 0x20000400)
T3220 001:014.810 - 5.382ms returns 0x184
T3220 001:014.908 JLINK_HasError()
T3220 001:014.990 JLINK_WriteReg(R0, 0x08000400)
T3220 001:015.030 - 0.054ms returns 0
T3220 001:015.063 JLINK_WriteReg(R1, 0x00000400)
T3220 001:015.094 - 0.045ms returns 0
T3220 001:015.126 JLINK_WriteReg(R2, 0x20000184)
T3220 001:015.156 - 0.045ms returns 0
T3220 001:015.188 JLINK_WriteReg(R3, 0x00000000)
T3220 001:015.219 - 0.045ms returns 0
T3220 001:015.251 JLINK_WriteReg(R4, 0x00000000)
T3220 001:015.281 - 0.044ms returns 0
T3220 001:015.313 JLINK_WriteReg(R5, 0x00000000)
T3220 001:015.343 - 0.044ms returns 0
T3220 001:015.375 JLINK_WriteReg(R6, 0x00000000)
T3220 001:015.404 - 0.046ms returns 0
T3220 001:015.438 JLINK_WriteReg(R7, 0x00000000)
T3220 001:015.468 - 0.044ms returns 0
T3220 001:015.500 JLINK_WriteReg(R8, 0x00000000)
T3220 001:015.535 - 0.050ms returns 0
T3220 001:015.567 JLINK_WriteReg(R9, 0x20000180)
T3220 001:015.597 - 0.044ms returns 0
T3220 001:015.629 JLINK_WriteReg(R10, 0x00000000)
T3220 001:015.659 - 0.079ms returns 0
T3220 001:015.750 JLINK_WriteReg(R11, 0x00000000)
T3220 001:015.801 - 0.069ms returns 0
T3220 001:015.839 JLINK_WriteReg(R12, 0x00000000)
T3220 001:015.875 - 0.053ms returns 0
T3220 001:015.912 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:015.948 - 0.053ms returns 0
T3220 001:015.985 JLINK_WriteReg(R14, 0x20000001)
T3220 001:016.020 - 0.052ms returns 0
T3220 001:016.057 JLINK_WriteReg(R15 (PC), 0x2000010C)
T3220 001:016.093 - 0.052ms returns 0
T3220 001:016.131 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:016.166 - 0.052ms returns 0
T3220 001:016.203 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:016.238 - 0.052ms returns 0
T3220 001:016.275 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:016.309 - 0.051ms returns 0
T3220 001:016.348 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:016.383 - 0.051ms returns 0
T3220 001:016.424 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:016.461 - 0.053ms returns 0x00000007
T3220 001:016.496 JLINK_Go()
T3220 001:016.542   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:020.096 - 3.640ms
T3220 001:020.169 JLINK_IsHalted()
T3220 001:020.703 - 0.556ms returns FALSE
T3220 001:020.751 JLINK_HasError()
T3220 001:026.519 JLINK_IsHalted()
T3220 001:029.896   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:030.555 - 4.086ms returns TRUE
T3220 001:030.635 JLINK_ReadReg(R15 (PC))
T3220 001:030.677 - 0.058ms returns 0x20000000
T3220 001:030.795 JLINK_ClrBPEx(BPHandle = 0x00000007)
T3220 001:030.837 - 0.057ms returns 0x00
T3220 001:030.873 JLINK_ReadReg(R0)
T3220 001:030.905 - 0.048ms returns 0x00000000
T3220 001:031.822 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T3220 001:031.865   Data:  89 68 C9 00 00 D5 40 1C 70 47 2D E9 F0 5F 05 46 ...
T3220 001:031.927   CPU_WriteMem(636 bytes @ 0x20000184)
T3220 001:039.924 - 8.148ms returns 0x27C
T3220 001:039.998 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T3220 001:040.035   Data:  01 68 3D D0 21 F0 80 01 01 60 60 7E 01 28 20 68 ...
T3220 001:040.102   CPU_WriteMem(388 bytes @ 0x20000400)
T3220 001:045.220 - 5.265ms returns 0x184
T3220 001:045.301 JLINK_HasError()
T3220 001:045.345 JLINK_WriteReg(R0, 0x08000800)
T3220 001:045.391 - 0.064ms returns 0
T3220 001:045.505 JLINK_WriteReg(R1, 0x00000400)
T3220 001:045.544 - 0.058ms returns 0
T3220 001:045.584 JLINK_WriteReg(R2, 0x20000184)
T3220 001:045.621 - 0.055ms returns 0
T3220 001:045.660 JLINK_WriteReg(R3, 0x00000000)
T3220 001:045.698 - 0.055ms returns 0
T3220 001:045.737 JLINK_WriteReg(R4, 0x00000000)
T3220 001:045.773 - 0.054ms returns 0
T3220 001:045.812 JLINK_WriteReg(R5, 0x00000000)
T3220 001:045.849 - 0.055ms returns 0
T3220 001:045.888 JLINK_WriteReg(R6, 0x00000000)
T3220 001:045.925 - 0.055ms returns 0
T3220 001:045.964 JLINK_WriteReg(R7, 0x00000000)
T3220 001:046.001 - 0.055ms returns 0
T3220 001:046.041 JLINK_WriteReg(R8, 0x00000000)
T3220 001:046.078 - 0.055ms returns 0
T3220 001:046.117 JLINK_WriteReg(R9, 0x20000180)
T3220 001:046.153 - 0.054ms returns 0
T3220 001:046.192 JLINK_WriteReg(R10, 0x00000000)
T3220 001:046.230 - 0.055ms returns 0
T3220 001:046.269 JLINK_WriteReg(R11, 0x00000000)
T3220 001:046.306 - 0.055ms returns 0
T3220 001:046.345 JLINK_WriteReg(R12, 0x00000000)
T3220 001:046.382 - 0.055ms returns 0
T3220 001:046.421 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:046.460 - 0.056ms returns 0
T3220 001:046.499 JLINK_WriteReg(R14, 0x20000001)
T3220 001:046.537 - 0.055ms returns 0
T3220 001:046.576 JLINK_WriteReg(R15 (PC), 0x2000010C)
T3220 001:046.614 - 0.055ms returns 0
T3220 001:046.653 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:046.690 - 0.055ms returns 0
T3220 001:046.730 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:046.766 - 0.055ms returns 0
T3220 001:046.806 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:046.842 - 0.055ms returns 0
T3220 001:046.881 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:046.918 - 0.055ms returns 0
T3220 001:046.958 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:046.998 - 0.058ms returns 0x00000008
T3220 001:047.038 JLINK_Go()
T3220 001:047.088   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:050.969 - 3.972ms
T3220 001:051.037 JLINK_IsHalted()
T3220 001:051.551 - 0.570ms returns FALSE
T3220 001:051.634 JLINK_HasError()
T3220 001:057.516 JLINK_IsHalted()
T3220 001:060.827   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:061.352 - 3.856ms returns TRUE
T3220 001:061.398 JLINK_ReadReg(R15 (PC))
T3220 001:061.437 - 0.057ms returns 0x20000000
T3220 001:061.475 JLINK_ClrBPEx(BPHandle = 0x00000008)
T3220 001:061.511 - 0.053ms returns 0x00
T3220 001:061.548 JLINK_ReadReg(R0)
T3220 001:061.583 - 0.052ms returns 0x00000000
T3220 001:062.469 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T3220 001:062.513   Data:  2F F9 05 46 0E E0 00 BF 00 F0 2A F9 40 1B 0A 28 ...
T3220 001:062.577   CPU_WriteMem(636 bytes @ 0x20000184)
T3220 001:070.442 - 8.014ms returns 0x27C
T3220 001:070.510 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T3220 001:070.546   Data:  0B 48 10 B5 01 68 41 F4 00 71 01 60 01 68 41 F4 ...
T3220 001:070.613   CPU_WriteMem(388 bytes @ 0x20000400)
T3220 001:075.671 - 5.200ms returns 0x184
T3220 001:075.745 JLINK_HasError()
T3220 001:075.841 JLINK_WriteReg(R0, 0x08000C00)
T3220 001:075.917 - 0.099ms returns 0
T3220 001:075.963 JLINK_WriteReg(R1, 0x00000400)
T3220 001:075.995 - 0.047ms returns 0
T3220 001:076.028 JLINK_WriteReg(R2, 0x20000184)
T3220 001:076.059 - 0.045ms returns 0
T3220 001:076.092 JLINK_WriteReg(R3, 0x00000000)
T3220 001:076.132 - 0.061ms returns 0
T3220 001:076.172 JLINK_WriteReg(R4, 0x00000000)
T3220 001:076.205 - 0.049ms returns 0
T3220 001:076.240 JLINK_WriteReg(R5, 0x00000000)
T3220 001:076.272 - 0.048ms returns 0
T3220 001:076.306 JLINK_WriteReg(R6, 0x00000000)
T3220 001:076.338 - 0.047ms returns 0
T3220 001:076.372 JLINK_WriteReg(R7, 0x00000000)
T3220 001:076.404 - 0.047ms returns 0
T3220 001:076.438 JLINK_WriteReg(R8, 0x00000000)
T3220 001:076.470 - 0.047ms returns 0
T3220 001:076.504 JLINK_WriteReg(R9, 0x20000180)
T3220 001:076.536 - 0.047ms returns 0
T3220 001:076.570 JLINK_WriteReg(R10, 0x00000000)
T3220 001:076.602 - 0.047ms returns 0
T3220 001:076.637 JLINK_WriteReg(R11, 0x00000000)
T3220 001:076.668 - 0.047ms returns 0
T3220 001:076.703 JLINK_WriteReg(R12, 0x00000000)
T3220 001:076.735 - 0.047ms returns 0
T3220 001:076.769 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:076.802 - 0.049ms returns 0
T3220 001:076.836 JLINK_WriteReg(R14, 0x20000001)
T3220 001:076.868 - 0.047ms returns 0
T3220 001:076.902 JLINK_WriteReg(R15 (PC), 0x2000010C)
T3220 001:076.935 - 0.048ms returns 0
T3220 001:076.969 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:077.001 - 0.048ms returns 0
T3220 001:077.035 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:077.067 - 0.047ms returns 0
T3220 001:077.102 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:077.133 - 0.047ms returns 0
T3220 001:077.168 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:077.200 - 0.048ms returns 0
T3220 001:077.235 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:077.270 - 0.051ms returns 0x00000009
T3220 001:077.305 JLINK_Go()
T3220 001:077.353   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:081.171 - 3.914ms
T3220 001:081.247 JLINK_IsHalted()
T3220 001:081.905 - 0.703ms returns FALSE
T3220 001:082.075 JLINK_HasError()
T3220 001:087.896 JLINK_IsHalted()
T3220 001:091.199   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:091.731 - 3.890ms returns TRUE
T3220 001:091.815 JLINK_ReadReg(R15 (PC))
T3220 001:091.860 - 0.064ms returns 0x20000000
T3220 001:091.901 JLINK_ClrBPEx(BPHandle = 0x00000009)
T3220 001:091.940 - 0.057ms returns 0x00
T3220 001:091.980 JLINK_ReadReg(R0)
T3220 001:092.017 - 0.055ms returns 0x00000000
T3220 001:092.928 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T3220 001:092.973   Data:  30 49 68 68 08 39 01 28 12 D0 02 28 13 D0 03 28 ...
T3220 001:093.037   CPU_WriteMem(636 bytes @ 0x20000184)
T3220 001:100.945 - 8.057ms returns 0x27C
T3220 001:101.013 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T3220 001:101.050   Data:  70 37 71 B1 01 21 01 60 FF F7 EC FD 06 46 04 E0 ...
T3220 001:101.116   CPU_WriteMem(388 bytes @ 0x20000400)
T3220 001:106.279 - 5.304ms returns 0x184
T3220 001:106.348 JLINK_HasError()
T3220 001:106.428 JLINK_WriteReg(R0, 0x08001000)
T3220 001:106.468 - 0.056ms returns 0
T3220 001:106.508 JLINK_WriteReg(R1, 0x00000400)
T3220 001:106.542 - 0.049ms returns 0
T3220 001:106.586 JLINK_WriteReg(R2, 0x20000184)
T3220 001:106.619 - 0.049ms returns 0
T3220 001:106.661 JLINK_WriteReg(R3, 0x00000000)
T3220 001:106.695 - 0.050ms returns 0
T3220 001:106.736 JLINK_WriteReg(R4, 0x00000000)
T3220 001:106.822 - 0.123ms returns 0
T3220 001:106.938 JLINK_WriteReg(R5, 0x00000000)
T3220 001:106.999 - 0.079ms returns 0
T3220 001:107.042 JLINK_WriteReg(R6, 0x00000000)
T3220 001:107.079 - 0.053ms returns 0
T3220 001:107.123 JLINK_WriteReg(R7, 0x00000000)
T3220 001:107.159 - 0.053ms returns 0
T3220 001:107.201 JLINK_WriteReg(R8, 0x00000000)
T3220 001:107.238 - 0.053ms returns 0
T3220 001:107.281 JLINK_WriteReg(R9, 0x20000180)
T3220 001:107.317 - 0.053ms returns 0
T3220 001:107.357 JLINK_WriteReg(R10, 0x00000000)
T3220 001:107.392 - 0.052ms returns 0
T3220 001:107.436 JLINK_WriteReg(R11, 0x00000000)
T3220 001:107.472 - 0.052ms returns 0
T3220 001:107.514 JLINK_WriteReg(R12, 0x00000000)
T3220 001:107.581 - 0.106ms returns 0
T3220 001:107.661 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:107.755 - 0.112ms returns 0
T3220 001:107.805 JLINK_WriteReg(R14, 0x20000001)
T3220 001:107.842 - 0.061ms returns 0
T3220 001:107.897 JLINK_WriteReg(R15 (PC), 0x2000010C)
T3220 001:107.935 - 0.054ms returns 0
T3220 001:107.979 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:108.016 - 0.059ms returns 0
T3220 001:108.064 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:108.100 - 0.053ms returns 0
T3220 001:108.143 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:108.179 - 0.053ms returns 0
T3220 001:108.221 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:108.257 - 0.053ms returns 0
T3220 001:108.300 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:108.340 - 0.057ms returns 0x0000000A
T3220 001:108.383 JLINK_Go()
T3220 001:108.433   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:112.036 - 3.697ms
T3220 001:112.116 JLINK_IsHalted()
T3220 001:112.683 - 0.589ms returns FALSE
T3220 001:112.737 JLINK_HasError()
T3220 001:118.793 JLINK_IsHalted()
T3220 001:122.387   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:122.940 - 4.186ms returns TRUE
T3220 001:123.006 JLINK_ReadReg(R15 (PC))
T3220 001:123.047 - 0.058ms returns 0x20000000
T3220 001:123.085 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T3220 001:123.121 - 0.053ms returns 0x00
T3220 001:123.159 JLINK_ReadReg(R0)
T3220 001:123.194 - 0.052ms returns 0x00000000
T3220 001:124.387 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T3220 001:124.435   Data:  01 20 38 66 FF F7 2C FD 04 46 06 E0 3E E0 00 BF ...
T3220 001:124.500   CPU_WriteMem(636 bytes @ 0x20000184)
T3220 001:132.470 - 8.124ms returns 0x27C
T3220 001:132.540 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T3220 001:132.579   Data:  00 28 01 D0 72 B6 FE E7 13 B0 30 BD 40 38 02 40 ...
T3220 001:132.761   CPU_WriteMem(388 bytes @ 0x20000400)
T3220 001:137.948 - 5.450ms returns 0x184
T3220 001:138.020 JLINK_HasError()
T3220 001:138.062 JLINK_WriteReg(R0, 0x08001400)
T3220 001:138.107 - 0.063ms returns 0
T3220 001:138.147 JLINK_WriteReg(R1, 0x0000033C)
T3220 001:138.185 - 0.056ms returns 0
T3220 001:138.224 JLINK_WriteReg(R2, 0x20000184)
T3220 001:138.261 - 0.055ms returns 0
T3220 001:138.299 JLINK_WriteReg(R3, 0x00000000)
T3220 001:138.336 - 0.054ms returns 0
T3220 001:138.375 JLINK_WriteReg(R4, 0x00000000)
T3220 001:138.412 - 0.054ms returns 0
T3220 001:138.451 JLINK_WriteReg(R5, 0x00000000)
T3220 001:138.488 - 0.054ms returns 0
T3220 001:138.527 JLINK_WriteReg(R6, 0x00000000)
T3220 001:138.564 - 0.055ms returns 0
T3220 001:138.603 JLINK_WriteReg(R7, 0x00000000)
T3220 001:138.640 - 0.055ms returns 0
T3220 001:138.679 JLINK_WriteReg(R8, 0x00000000)
T3220 001:138.716 - 0.055ms returns 0
T3220 001:138.756 JLINK_WriteReg(R9, 0x20000180)
T3220 001:138.792 - 0.054ms returns 0
T3220 001:138.829 JLINK_WriteReg(R10, 0x00000000)
T3220 001:138.864 - 0.052ms returns 0
T3220 001:138.900 JLINK_WriteReg(R11, 0x00000000)
T3220 001:138.935 - 0.051ms returns 0
T3220 001:138.972 JLINK_WriteReg(R12, 0x00000000)
T3220 001:139.007 - 0.051ms returns 0
T3220 001:139.043 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:139.079 - 0.052ms returns 0
T3220 001:139.116 JLINK_WriteReg(R14, 0x20000001)
T3220 001:139.151 - 0.051ms returns 0
T3220 001:139.188 JLINK_WriteReg(R15 (PC), 0x2000010C)
T3220 001:139.223 - 0.052ms returns 0
T3220 001:139.259 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:139.294 - 0.052ms returns 0
T3220 001:139.331 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:139.366 - 0.051ms returns 0
T3220 001:139.402 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:139.437 - 0.051ms returns 0
T3220 001:139.474 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:139.508 - 0.051ms returns 0
T3220 001:139.546 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:139.583 - 0.054ms returns 0x0000000B
T3220 001:139.620 JLINK_Go()
T3220 001:139.667   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:143.409 - 3.819ms
T3220 001:143.464 JLINK_IsHalted()
T3220 001:143.937 - 0.513ms returns FALSE
T3220 001:144.003 JLINK_HasError()
T3220 001:148.925 JLINK_IsHalted()
T3220 001:152.268   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:152.762 - 3.879ms returns TRUE
T3220 001:152.838 JLINK_ReadReg(R15 (PC))
T3220 001:152.887 - 0.070ms returns 0x20000000
T3220 001:152.929 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T3220 001:152.966 - 0.053ms returns 0x00
T3220 001:153.009 JLINK_ReadReg(R0)
T3220 001:153.046 - 0.054ms returns 0x00000000
T3220 001:153.136 JLINK_HasError()
T3220 001:153.174 JLINK_WriteReg(R0, 0x00000002)
T3220 001:153.211 - 0.053ms returns 0
T3220 001:153.248 JLINK_WriteReg(R1, 0x0000033C)
T3220 001:153.283 - 0.052ms returns 0
T3220 001:153.320 JLINK_WriteReg(R2, 0x20000184)
T3220 001:153.355 - 0.052ms returns 0
T3220 001:153.392 JLINK_WriteReg(R3, 0x00000000)
T3220 001:153.427 - 0.051ms returns 0
T3220 001:153.464 JLINK_WriteReg(R4, 0x00000000)
T3220 001:153.499 - 0.052ms returns 0
T3220 001:153.536 JLINK_WriteReg(R5, 0x00000000)
T3220 001:153.571 - 0.051ms returns 0
T3220 001:153.607 JLINK_WriteReg(R6, 0x00000000)
T3220 001:153.642 - 0.051ms returns 0
T3220 001:153.679 JLINK_WriteReg(R7, 0x00000000)
T3220 001:153.714 - 0.052ms returns 0
T3220 001:153.750 JLINK_WriteReg(R8, 0x00000000)
T3220 001:153.786 - 0.052ms returns 0
T3220 001:153.823 JLINK_WriteReg(R9, 0x20000180)
T3220 001:153.857 - 0.056ms returns 0
T3220 001:153.901 JLINK_WriteReg(R10, 0x00000000)
T3220 001:153.937 - 0.053ms returns 0
T3220 001:153.974 JLINK_WriteReg(R11, 0x00000000)
T3220 001:154.009 - 0.051ms returns 0
T3220 001:154.046 JLINK_WriteReg(R12, 0x00000000)
T3220 001:154.080 - 0.051ms returns 0
T3220 001:154.117 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:154.153 - 0.052ms returns 0
T3220 001:154.189 JLINK_WriteReg(R14, 0x20000001)
T3220 001:154.224 - 0.051ms returns 0
T3220 001:154.261 JLINK_WriteReg(R15 (PC), 0x20000086)
T3220 001:154.296 - 0.052ms returns 0
T3220 001:154.333 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:154.368 - 0.052ms returns 0
T3220 001:154.405 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:154.439 - 0.051ms returns 0
T3220 001:154.476 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:154.511 - 0.051ms returns 0
T3220 001:154.548 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:154.583 - 0.051ms returns 0
T3220 001:154.620 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:154.656 - 0.053ms returns 0x0000000C
T3220 001:154.693 JLINK_Go()
T3220 001:154.739   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:158.588 - 3.929ms
T3220 001:158.647 JLINK_IsHalted()
T3220 001:161.974   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:162.505 - 3.900ms returns TRUE
T3220 001:162.571 JLINK_ReadReg(R15 (PC))
T3220 001:162.609 - 0.055ms returns 0x20000000
T3220 001:162.647 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T3220 001:162.684 - 0.053ms returns 0x00
T3220 001:162.718 JLINK_ReadReg(R0)
T3220 001:162.734 - 0.021ms returns 0x00000000
T3220 001:238.193 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T3220 001:238.249   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T3220 001:238.325   CPU_WriteMem(388 bytes @ 0x20000000)
T3220 001:243.509 - 5.359ms returns 0x184
T3220 001:243.633 JLINK_HasError()
T3220 001:243.673 JLINK_WriteReg(R0, 0x08000000)
T3220 001:243.714 - 0.056ms returns 0
T3220 001:243.749 JLINK_WriteReg(R1, 0x017D7840)
T3220 001:243.783 - 0.050ms returns 0
T3220 001:243.818 JLINK_WriteReg(R2, 0x00000003)
T3220 001:243.851 - 0.049ms returns 0
T3220 001:243.885 JLINK_WriteReg(R3, 0x00000000)
T3220 001:243.918 - 0.049ms returns 0
T3220 001:243.953 JLINK_WriteReg(R4, 0x00000000)
T3220 001:243.985 - 0.048ms returns 0
T3220 001:244.020 JLINK_WriteReg(R5, 0x00000000)
T3220 001:244.053 - 0.048ms returns 0
T3220 001:244.088 JLINK_WriteReg(R6, 0x00000000)
T3220 001:244.120 - 0.048ms returns 0
T3220 001:244.155 JLINK_WriteReg(R7, 0x00000000)
T3220 001:244.188 - 0.049ms returns 0
T3220 001:244.222 JLINK_WriteReg(R8, 0x00000000)
T3220 001:244.255 - 0.049ms returns 0
T3220 001:244.290 JLINK_WriteReg(R9, 0x20000180)
T3220 001:244.322 - 0.048ms returns 0
T3220 001:244.357 JLINK_WriteReg(R10, 0x00000000)
T3220 001:244.390 - 0.049ms returns 0
T3220 001:244.424 JLINK_WriteReg(R11, 0x00000000)
T3220 001:244.457 - 0.048ms returns 0
T3220 001:244.492 JLINK_WriteReg(R12, 0x00000000)
T3220 001:244.531 - 0.058ms returns 0
T3220 001:244.569 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:244.604 - 0.050ms returns 0
T3220 001:244.638 JLINK_WriteReg(R14, 0x20000001)
T3220 001:244.671 - 0.049ms returns 0
T3220 001:244.706 JLINK_WriteReg(R15 (PC), 0x20000054)
T3220 001:244.739 - 0.049ms returns 0
T3220 001:244.774 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:244.808 - 0.049ms returns 0
T3220 001:244.841 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:244.872 - 0.046ms returns 0
T3220 001:244.905 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:244.936 - 0.046ms returns 0
T3220 001:244.969 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:245.000 - 0.046ms returns 0
T3220 001:245.033 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:245.073   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:245.587 - 0.594ms returns 0x0000000D
T3220 001:245.653 JLINK_Go()
T3220 001:245.696   CPU_WriteMem(2 bytes @ 0x20000000)
T3220 001:246.234   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:249.825 - 4.199ms
T3220 001:249.877 JLINK_IsHalted()
T3220 001:253.086   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:253.621 - 3.775ms returns TRUE
T3220 001:253.677 JLINK_ReadReg(R15 (PC))
T3220 001:253.713 - 0.051ms returns 0x20000000
T3220 001:253.747 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T3220 001:253.779 - 0.047ms returns 0x00
T3220 001:253.813 JLINK_ReadReg(R0)
T3220 001:253.844 - 0.046ms returns 0x00000000
T3220 001:253.878 JLINK_HasError()
T3220 001:253.912 JLINK_WriteReg(R0, 0xFFFFFFFF)
T3220 001:253.944 - 0.047ms returns 0
T3220 001:253.977 JLINK_WriteReg(R1, 0x08000000)
T3220 001:254.008 - 0.046ms returns 0
T3220 001:254.041 JLINK_WriteReg(R2, 0x0000173C)
T3220 001:254.072 - 0.046ms returns 0
T3220 001:254.105 JLINK_WriteReg(R3, 0x04C11DB7)
T3220 001:254.136 - 0.046ms returns 0
T3220 001:254.169 JLINK_WriteReg(R4, 0x00000000)
T3220 001:254.200 - 0.046ms returns 0
T3220 001:254.233 JLINK_WriteReg(R5, 0x00000000)
T3220 001:254.265 - 0.046ms returns 0
T3220 001:254.298 JLINK_WriteReg(R6, 0x00000000)
T3220 001:254.329 - 0.046ms returns 0
T3220 001:254.362 JLINK_WriteReg(R7, 0x00000000)
T3220 001:254.393 - 0.046ms returns 0
T3220 001:254.426 JLINK_WriteReg(R8, 0x00000000)
T3220 001:254.457 - 0.046ms returns 0
T3220 001:254.490 JLINK_WriteReg(R9, 0x20000180)
T3220 001:254.521 - 0.046ms returns 0
T3220 001:254.554 JLINK_WriteReg(R10, 0x00000000)
T3220 001:254.585 - 0.046ms returns 0
T3220 001:254.619 JLINK_WriteReg(R11, 0x00000000)
T3220 001:254.650 - 0.047ms returns 0
T3220 001:254.683 JLINK_WriteReg(R12, 0x00000000)
T3220 001:254.712 - 0.044ms returns 0
T3220 001:254.743 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:254.774 - 0.044ms returns 0
T3220 001:254.805 JLINK_WriteReg(R14, 0x20000001)
T3220 001:254.835 - 0.044ms returns 0
T3220 001:254.866 JLINK_WriteReg(R15 (PC), 0x20000002)
T3220 001:254.895 - 0.044ms returns 0
T3220 001:254.927 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:254.956 - 0.044ms returns 0
T3220 001:254.988 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:255.017 - 0.044ms returns 0
T3220 001:255.049 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:255.078 - 0.043ms returns 0
T3220 001:255.110 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:255.139 - 0.044ms returns 0
T3220 001:255.171 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:255.201 - 0.045ms returns 0x0000000E
T3220 001:255.233 JLINK_Go()
T3220 001:255.271   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:259.057 - 3.846ms
T3220 001:259.100 JLINK_IsHalted()
T3220 001:259.528 - 0.479ms returns FALSE
T3220 001:259.601 JLINK_HasError()
T3220 001:285.901 JLINK_IsHalted()
T3220 001:286.475 - 0.597ms returns FALSE
T3220 001:286.525 JLINK_HasError()
T3220 001:301.345 JLINK_IsHalted()
T3220 001:304.664   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:305.234 - 3.918ms returns TRUE
T3220 001:305.290 JLINK_ReadReg(R15 (PC))
T3220 001:305.376 - 0.101ms returns 0x20000000
T3220 001:305.453 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T3220 001:305.483 - 0.044ms returns 0x00
T3220 001:305.517 JLINK_ReadReg(R0)
T3220 001:305.552 - 0.053ms returns 0xA3261E59
T3220 001:306.565 JLINK_HasError()
T3220 001:306.600 JLINK_WriteReg(R0, 0x00000003)
T3220 001:306.630 - 0.043ms returns 0
T3220 001:306.659 JLINK_WriteReg(R1, 0x08000000)
T3220 001:306.686 - 0.040ms returns 0
T3220 001:306.714 JLINK_WriteReg(R2, 0x0000173C)
T3220 001:306.741 - 0.040ms returns 0
T3220 001:306.771 JLINK_WriteReg(R3, 0x04C11DB7)
T3220 001:306.795 - 0.036ms returns 0
T3220 001:306.821 JLINK_WriteReg(R4, 0x00000000)
T3220 001:306.846 - 0.036ms returns 0
T3220 001:306.872 JLINK_WriteReg(R5, 0x00000000)
T3220 001:306.896 - 0.036ms returns 0
T3220 001:306.922 JLINK_WriteReg(R6, 0x00000000)
T3220 001:306.947 - 0.036ms returns 0
T3220 001:306.973 JLINK_WriteReg(R7, 0x00000000)
T3220 001:306.997 - 0.036ms returns 0
T3220 001:307.023 JLINK_WriteReg(R8, 0x00000000)
T3220 001:307.048 - 0.036ms returns 0
T3220 001:307.074 JLINK_WriteReg(R9, 0x20000180)
T3220 001:307.098 - 0.036ms returns 0
T3220 001:307.124 JLINK_WriteReg(R10, 0x00000000)
T3220 001:307.149 - 0.036ms returns 0
T3220 001:307.175 JLINK_WriteReg(R11, 0x00000000)
T3220 001:307.199 - 0.036ms returns 0
T3220 001:307.225 JLINK_WriteReg(R12, 0x00000000)
T3220 001:307.250 - 0.036ms returns 0
T3220 001:307.276 JLINK_WriteReg(R13 (SP), 0x20001000)
T3220 001:307.301 - 0.037ms returns 0
T3220 001:307.328 JLINK_WriteReg(R14, 0x20000001)
T3220 001:307.352 - 0.036ms returns 0
T3220 001:307.378 JLINK_WriteReg(R15 (PC), 0x20000086)
T3220 001:307.403 - 0.036ms returns 0
T3220 001:307.429 JLINK_WriteReg(XPSR, 0x01000000)
T3220 001:307.454 - 0.036ms returns 0
T3220 001:307.480 JLINK_WriteReg(MSP, 0x20001000)
T3220 001:307.504 - 0.036ms returns 0
T3220 001:307.531 JLINK_WriteReg(PSP, 0x20001000)
T3220 001:307.555 - 0.035ms returns 0
T3220 001:307.580 JLINK_WriteReg(CFBP, 0x00000000)
T3220 001:307.604 - 0.035ms returns 0
T3220 001:307.629 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T3220 001:307.654 - 0.037ms returns 0x0000000F
T3220 001:307.680 JLINK_Go()
T3220 001:307.713   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:311.634 - 3.984ms
T3220 001:311.683 JLINK_IsHalted()
T3220 001:314.983   CPU_ReadMem(2 bytes @ 0x20000000)
T3220 001:315.499 - 3.852ms returns TRUE
T3220 001:315.558 JLINK_ReadReg(R15 (PC))
T3220 001:315.594 - 0.050ms returns 0x20000000
T3220 001:315.626 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T3220 001:315.657 - 0.045ms returns 0x00
T3220 001:315.689 JLINK_ReadReg(R0)
T3220 001:315.719 - 0.044ms returns 0x00000000
T3220 001:385.675 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T3220 001:385.751   Data:  FE E7
T3220 001:385.828   CPU_WriteMem(2 bytes @ 0x20000000)
T3220 001:386.355 - 0.702ms returns 0x2
T3220 001:386.402 JLINK_HasError()
T3220 001:402.168 JLINK_Close()
T3220 001:402.482   CPU_ReadMem(4 bytes @ 0xE0001000)
T3220 001:414.781 - 12.646ms
T3220 001:414.831   
T3220 001:414.863   Closed
