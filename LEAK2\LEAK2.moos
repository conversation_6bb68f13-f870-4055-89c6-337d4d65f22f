//------------------------------------------------
// LEAK2 config block

ProcessConfig = LEAK2
{
   AppTick   = 10
   CommsTick = 10

       // 虚拟机IP地址，用于接收数据
    RecvIP = ************

    // 接收数据的端口号，对应CAN盒子的目标端口1
    RecvPort = 8001

    // CAN盒子的IP地址
    // 用于发送数据到CAN盒子
    DestIP = *************

    // 发送数据的目标端口号（CAN盒子的工作端口）
    DestPort = 4001

    // BlueSocket重试机制配置
    max_retries = 5             // 最大重试次数
    retry_delay = 2             // 每次重试之间的延迟（秒）
}

//------------------------------------------------
// 设备控制示例命令
//
// 抛载控制示例：
// uPokeDB LOAD_ACTION=true        // 开启抛载
// uPokeDB LOAD_ACTION=false       // 关闭抛载
//
// 设备控制消息示例：
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=on;"
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=off;"
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=DVL;Power=on;"
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=ALL;Power=off;"