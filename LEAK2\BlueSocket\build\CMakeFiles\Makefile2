# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# The main recursive all target
all:

.PHONY : all

# The main recursive preinstall target
preinstall:

.PHONY : preinstall

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build

#=============================================================================
# Target rules for target CMakeFiles/BlueSocket.dir

# All Build rule for target.
CMakeFiles/BlueSocket.dir/all:
	$(MAKE) -f CMakeFiles/BlueSocket.dir/build.make CMakeFiles/BlueSocket.dir/depend
	$(MAKE) -f CMakeFiles/BlueSocket.dir/build.make CMakeFiles/BlueSocket.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build/CMakeFiles --progress-num=1,2 "Built target BlueSocket"
.PHONY : CMakeFiles/BlueSocket.dir/all

# Include target in all.
all: CMakeFiles/BlueSocket.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
CMakeFiles/BlueSocket.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/BlueSocket.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build/CMakeFiles 0
.PHONY : CMakeFiles/BlueSocket.dir/rule

# Convenience name for target.
BlueSocket: CMakeFiles/BlueSocket.dir/rule

.PHONY : BlueSocket

# clean rule for target.
CMakeFiles/BlueSocket.dir/clean:
	$(MAKE) -f CMakeFiles/BlueSocket.dir/build.make CMakeFiles/BlueSocket.dir/clean
.PHONY : CMakeFiles/BlueSocket.dir/clean

# clean rule for target.
clean: CMakeFiles/BlueSocket.dir/clean

.PHONY : clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

